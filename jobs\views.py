from django.shortcuts import render, get_object_or_404, redirect
from django.views.generic import Template<PERSON>iew, ListView, DetailView, CreateView, UpdateView, DeleteView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.http import JsonResponse
from django.contrib import messages
from django.urls import reverse_lazy
from django.db.models import Count, Q
from django.utils import timezone
from datetime import timedelta

from .models import Job, JobExecution, JobSchedule, TaskResult, CollectionMetrics, JobQueue
from aws_session.models import AWSSession
from inventory.models import AWSAccount


class JobsDashboardView(LoginRequiredMixin, TemplateView):
    template_name = 'jobs/dashboard.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Job statistics
        context['total_jobs'] = Job.objects.filter(is_active=True).count()
        context['scheduled_jobs'] = JobSchedule.objects.filter(is_enabled=True).count()
        context['running_executions'] = JobExecution.objects.filter(status='running').count()
        context['failed_executions'] = JobExecution.objects.filter(
            status='failed',
            created_at__gte=timezone.now() - timedelta(days=7)
        ).count()

        # Recent executions
        context['recent_executions'] = JobExecution.objects.select_related(
            'job', 'started_by'
        ).order_by('-created_at')[:10]

        # Job queue
        context['queued_jobs'] = JobQueue.objects.select_related(
            'job_execution__job'
        ).order_by('-priority', 'queued_at')[:5]

        # Execution status distribution
        status_counts = JobExecution.objects.filter(
            created_at__gte=timezone.now() - timedelta(days=30)
        ).values('status').annotate(count=Count('id'))
        context['status_distribution'] = {item['status']: item['count'] for item in status_counts}

        return context


class JobListView(LoginRequiredMixin, ListView):
    model = Job
    template_name = 'jobs/job_list.html'
    context_object_name = 'jobs'
    paginate_by = 20

    def get_queryset(self):
        queryset = Job.objects.select_related('aws_session', 'created_by').filter(is_active=True)

        # Filter by category (inventory vs automation)
        category = self.request.GET.get('category')
        if category == 'inventory':
            # Show only data collection jobs (inventory-related)
            queryset = queryset.filter(job_type__in=[
                'full_collection', 'incremental_collection',
                'specific_resource', 'account_specific'
            ])
        elif category == 'automation':
            # Show only automation jobs (if any exist in this model)
            # For now, this would be empty as automation jobs are in operations app
            queryset = queryset.none()

        job_type = self.request.GET.get('type')
        if job_type:
            queryset = queryset.filter(job_type=job_type)

        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) | Q(description__icontains=search)
            )

        return queryset.order_by('-created_at')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['job_types'] = Job.JOB_TYPES
        context['current_filters'] = {
            'type': self.request.GET.get('type', ''),
            'search': self.request.GET.get('search', ''),
            'category': self.request.GET.get('category', ''),
        }

        # Update page title based on category
        category = self.request.GET.get('category')
        if category == 'inventory':
            context['page_title'] = 'Data Collection Jobs'
            context['page_description'] = 'Manage AWS resource collection and inventory jobs'
        else:
            context['page_title'] = 'Data Collection Jobs'
            context['page_description'] = 'Manage and monitor AWS resource collection jobs'

        return context


class JobCreateView(LoginRequiredMixin, TemplateView):
    template_name = 'jobs/job_create.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['aws_sessions'] = AWSSession.objects.filter(is_active=True)
        context['aws_accounts'] = AWSAccount.objects.filter(is_active=True)
        context['resource_types'] = Job.RESOURCE_TYPES
        return context

    def post(self, request, *args, **kwargs):
        try:
            # Get form data
            name = request.POST.get('name')
            description = request.POST.get('description', '')
            job_type = request.POST.get('job_type')
            aws_session_id = request.POST.get('aws_session')

            # Get resource types
            resource_types = request.POST.getlist('resource_types')
            if not resource_types:
                # Try JSON format
                import json
                resource_types_json = request.POST.get('resource_types_json')
                if resource_types_json:
                    resource_types = json.loads(resource_types_json)

            # Get performance settings
            parallel_accounts = int(request.POST.get('parallel_accounts', 3))
            parallel_regions = int(request.POST.get('parallel_regions', 2))
            parallel_resources = int(request.POST.get('parallel_resources', 5))
            timeout_minutes = int(request.POST.get('timeout_minutes', 60))
            retry_count = int(request.POST.get('retry_count', 3))

            # Validate required fields
            if not all([name, job_type, aws_session_id]):
                messages.error(request, 'Please fill in all required fields.')
                return self.get(request, *args, **kwargs)

            if not resource_types:
                messages.error(request, 'Please select at least one resource type.')
                return self.get(request, *args, **kwargs)

            # Get AWS session
            aws_session = AWSSession.objects.get(id=aws_session_id)

            # Create job
            job = Job.objects.create(
                name=name,
                description=description,
                job_type=job_type,
                resource_types=resource_types,
                aws_session=aws_session,
                parallel_accounts=parallel_accounts,
                parallel_regions=parallel_regions,
                parallel_resources=parallel_resources,
                timeout_minutes=timeout_minutes,
                retry_count=retry_count,
                created_by=request.user
            )

            messages.success(request, f'Job "{job.name}" created successfully.')

            # Check if we should run immediately
            if request.POST.get('run_immediately'):
                # Create and run execution
                execution = JobExecution.objects.create(
                    job=job,
                    started_by=request.user,
                    status='pending'
                )

                JobQueue.objects.create(
                    job_execution=execution,
                    priority=2
                )

                # Run job asynchronously
                from .collectors.runner import run_job_execution
                import threading

                def run_async():
                    run_job_execution(str(execution.id))

                thread = threading.Thread(target=run_async)
                thread.start()

                messages.success(request, f'Job started! Execution ID: {execution.id}')
                return redirect('jobs:execution_detail', pk=execution.pk)

            return redirect('jobs:job_detail', pk=job.pk)

        except Exception as e:
            messages.error(request, f'Error creating job: {str(e)}')
            return self.get(request, *args, **kwargs)


class JobDetailView(LoginRequiredMixin, DetailView):
    model = Job
    template_name = 'jobs/job_detail.html'
    context_object_name = 'job'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        job = self.get_object()

        # Recent executions
        context['recent_executions'] = job.executions.order_by('-created_at')[:10]

        # Execution statistics
        context['execution_stats'] = {
            'total': job.executions.count(),
            'completed': job.executions.filter(status='completed').count(),
            'failed': job.executions.filter(status='failed').count(),
            'running': job.executions.filter(status='running').count(),
        }

        # Schedule info
        try:
            context['schedule'] = job.schedule
        except JobSchedule.DoesNotExist:
            context['schedule'] = None

        return context


class JobEditView(LoginRequiredMixin, UpdateView):
    model = Job
    template_name = 'jobs/job_edit.html'
    fields = ['name', 'description', 'job_type', 'resource_types', 'aws_session',
              'parallel_accounts', 'parallel_regions', 'parallel_resources',
              'timeout_minutes', 'retry_count', 'is_active']

    def get_success_url(self):
        return reverse_lazy('jobs:job_detail', kwargs={'pk': self.object.pk})

    def form_valid(self, form):
        messages.success(self.request, f'Job "{form.instance.name}" updated successfully.')
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['aws_sessions'] = AWSSession.objects.filter(is_active=True)
        context['aws_accounts'] = AWSAccount.objects.filter(is_active=True)
        context['resource_types'] = Job.RESOURCE_TYPES
        return context


class JobDeleteView(LoginRequiredMixin, DeleteView):
    model = Job
    template_name = 'jobs/job_delete.html'
    success_url = reverse_lazy('jobs:job_list')

    def delete(self, request, *args, **kwargs):
        job = self.get_object()
        job.is_active = False
        job.save()
        messages.success(request, f'Job "{job.name}" deactivated successfully.')
        return redirect(self.success_url)


class JobRunView(LoginRequiredMixin, TemplateView):
    template_name = 'jobs/job_run.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['job'] = get_object_or_404(Job, pk=kwargs['pk'])
        return context

    def post(self, request, pk):
        job = get_object_or_404(Job, pk=pk)

        # Create job execution
        execution = JobExecution.objects.create(
            job=job,
            started_by=request.user,
            started_at=timezone.now(),
            status='pending'
        )

        # Add to queue
        JobQueue.objects.create(
            job_execution=execution,
            priority=request.POST.get('priority', 2)
        )

        messages.success(request, f'Job "{job.name}" queued for execution.')
        return redirect('jobs:execution_detail', pk=execution.pk)


class JobExecutionListView(LoginRequiredMixin, ListView):
    model = JobExecution
    template_name = 'jobs/execution_list.html'
    context_object_name = 'executions'
    paginate_by = 20

    def get_queryset(self):
        queryset = JobExecution.objects.select_related('job', 'started_by')

        # Filter by category (inventory vs automation)
        category = self.request.GET.get('category')
        if category == 'inventory':
            # Show only data collection job executions
            queryset = queryset.filter(job__job_type__in=[
                'full_collection', 'incremental_collection',
                'specific_resource', 'account_specific'
            ])
        elif category == 'automation':
            # Show only automation job executions (none for now)
            queryset = queryset.none()

        status = self.request.GET.get('status')
        if status:
            queryset = queryset.filter(status=status)

        job_id = self.request.GET.get('job')
        if job_id:
            queryset = queryset.filter(job_id=job_id)

        return queryset.order_by('-created_at')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['status_choices'] = JobExecution.STATUS_CHOICES
        context['jobs'] = Job.objects.filter(is_active=True)
        context['current_filters'] = {
            'status': self.request.GET.get('status', ''),
            'job': self.request.GET.get('job', ''),
            'category': self.request.GET.get('category', ''),
        }

        # Update page title based on category
        category = self.request.GET.get('category')
        if category == 'inventory':
            context['page_title'] = 'Data Collection Job History'
            context['page_description'] = 'View execution history for AWS resource collection jobs'
        else:
            context['page_title'] = 'Job Execution History'
            context['page_description'] = 'View execution history for all jobs'

        return context


class JobExecutionDetailView(LoginRequiredMixin, DetailView):
    model = JobExecution
    template_name = 'jobs/execution_detail.html'
    context_object_name = 'execution'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        execution = self.get_object()

        # Task results
        context['task_results'] = execution.task_results.order_by('-created_at')

        # Metrics
        try:
            context['metrics'] = execution.metrics
        except CollectionMetrics.DoesNotExist:
            context['metrics'] = None

        return context


class JobExecutionStopView(LoginRequiredMixin, TemplateView):
    template_name = 'jobs/execution_stop.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['execution'] = get_object_or_404(JobExecution, pk=kwargs['pk'])
        return context

    def post(self, request, pk):
        execution = get_object_or_404(JobExecution, pk=pk)

        if execution.status == 'running':
            execution.status = 'cancelled'
            execution.completed_at = timezone.now()
            execution.save()

            # TODO: Cancel Celery task if celery_task_id exists

            messages.success(request, f'Job execution stopped successfully.')
        else:
            messages.warning(request, 'Job execution is not running.')

        return redirect('jobs:execution_detail', pk=execution.pk)


class JobExecutionRetryView(LoginRequiredMixin, TemplateView):
    template_name = 'jobs/execution_retry.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['execution'] = get_object_or_404(JobExecution, pk=kwargs['pk'])
        return context

    def post(self, request, pk):
        original_execution = get_object_or_404(JobExecution, pk=pk)

        # Create new execution
        new_execution = JobExecution.objects.create(
            job=original_execution.job,
            started_by=request.user,
            started_at=timezone.now(),
            status='pending'
        )

        # Add to queue
        JobQueue.objects.create(
            job_execution=new_execution,
            priority=request.POST.get('priority', 2)
        )

        messages.success(request, f'Job execution retried successfully.')
        return redirect('jobs:execution_detail', pk=new_execution.pk)


class JobScheduleListView(LoginRequiredMixin, ListView):
    model = JobSchedule
    template_name = 'jobs/schedule_list.html'
    context_object_name = 'schedules'
    paginate_by = 20

    def get_queryset(self):
        return JobSchedule.objects.select_related('job').order_by('-last_run')


class JobScheduleDetailView(LoginRequiredMixin, DetailView):
    model = JobSchedule
    template_name = 'jobs/schedule_detail.html'
    context_object_name = 'schedule'


class JobMetricsView(LoginRequiredMixin, TemplateView):
    template_name = 'jobs/metrics.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Overall metrics
        context['total_executions'] = JobExecution.objects.count()
        context['successful_executions'] = JobExecution.objects.filter(status='completed').count()
        context['failed_executions'] = JobExecution.objects.filter(status='failed').count()

        # Recent metrics
        recent_metrics = CollectionMetrics.objects.select_related(
            'job_execution__job'
        ).order_by('-created_at')[:10]
        context['recent_metrics'] = recent_metrics

        # Performance data for charts
        context['execution_trends'] = self.get_execution_trends()
        context['resource_collection_stats'] = self.get_resource_collection_stats()

        return context

    def get_execution_trends(self):
        # Get execution counts by day for the last 30 days
        from django.db.models import Count
        from django.db.models.functions import TruncDate

        trends = JobExecution.objects.filter(
            created_at__gte=timezone.now() - timedelta(days=30)
        ).annotate(
            date=TruncDate('created_at')
        ).values('date').annotate(
            count=Count('id')
        ).order_by('date')

        return list(trends)

    def get_resource_collection_stats(self):
        # Get resource collection statistics
        metrics = CollectionMetrics.objects.aggregate(
            total_resources=Count('total_resources_found'),
            total_api_calls=Count('total_api_calls'),
            avg_execution_time=Count('total_execution_time')
        )
        return metrics


class WorkerStatusView(LoginRequiredMixin, TemplateView):
    template_name = 'jobs/worker_status.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Mock worker status data (would be real in production with Celery)
        context['workers'] = [
            {
                'name': 'worker-1',
                'status': 'online',
                'active_tasks': 2,
                'processed_tasks': 150,
                'last_heartbeat': timezone.now() - timedelta(seconds=30)
            },
            {
                'name': 'worker-2',
                'status': 'online',
                'active_tasks': 1,
                'processed_tasks': 89,
                'last_heartbeat': timezone.now() - timedelta(seconds=45)
            }
        ]

        context['queue_stats'] = {
            'pending': JobQueue.objects.filter(job_execution__status='pending').count(),
            'running': JobExecution.objects.filter(status='running').count(),
            'completed_today': JobExecution.objects.filter(
                status='completed',
                completed_at__date=timezone.now().date()
            ).count()
        }

        return context


class JobQueueView(LoginRequiredMixin, ListView):
    model = JobQueue
    template_name = 'jobs/job_queue.html'
    context_object_name = 'queue_items'
    paginate_by = 20

    def get_queryset(self):
        return JobQueue.objects.select_related(
            'job_execution__job', 'job_execution__started_by'
        ).order_by('-priority', 'queued_at')


# API Views
class JobListAPIView(LoginRequiredMixin, TemplateView):
    def get(self, request, *args, **kwargs):
        jobs = Job.objects.filter(is_active=True).select_related('created_by', 'aws_session')

        data = {
            'jobs': [
                {
                    'id': str(job.id),
                    'name': job.name,
                    'job_type': job.job_type,
                    'created_by': job.created_by.username,
                    'created_at': job.created_at.isoformat(),
                    'is_active': job.is_active
                }
                for job in jobs
            ]
        }

        return JsonResponse(data)


class JobRunAPIView(LoginRequiredMixin, TemplateView):
    def post(self, request, pk, *args, **kwargs):
        try:
            job = get_object_or_404(Job, pk=pk)

            # Create job execution
            execution = JobExecution.objects.create(
                job=job,
                started_by=request.user,
                status='pending'
            )

            # Add to queue
            queue_entry = JobQueue.objects.create(
                job_execution=execution,
                priority=2  # Normal priority
            )

            # For demo purposes, run the job immediately
            # In production, this would be handled by Celery
            from .collectors.runner import run_job_execution
            import threading

            def run_async():
                run_job_execution(str(execution.id))

            thread = threading.Thread(target=run_async)
            thread.start()

            return JsonResponse({
                'status': 'success',
                'execution_id': str(execution.id),
                'message': 'Job queued for execution'
            })

        except Exception as e:
            return JsonResponse({
                'status': 'error',
                'error': str(e)
            }, status=400)


class JobExecutionListAPIView(LoginRequiredMixin, TemplateView):
    def get(self, request, *args, **kwargs):
        return JsonResponse({'status': 'not_implemented'})


class JobExecutionStatusAPIView(LoginRequiredMixin, TemplateView):
    def get(self, request, pk, *args, **kwargs):
        return JsonResponse({'status': 'not_implemented'})


class JobMetricsAPIView(LoginRequiredMixin, TemplateView):
    def get(self, request, *args, **kwargs):
        return JsonResponse({'status': 'not_implemented'})


class WorkerStatusAPIView(LoginRequiredMixin, TemplateView):
    def get(self, request, *args, **kwargs):
        return JsonResponse({'status': 'not_implemented'})
