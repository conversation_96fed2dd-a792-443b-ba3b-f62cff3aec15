from django.db import models
from django.contrib.auth.models import User
import uuid
import json


class AutomationTemplate(models.Model):
    """Template for automation tasks"""
    TEMPLATE_TYPES = [
        ('resource_cleanup', 'Resource Cleanup'),
        ('security_audit', 'Security Audit'),
        ('cost_optimization', 'Cost Optimization'),
        ('compliance_check', 'Compliance Check'),
        ('backup_verification', 'Backup Verification'),
        ('performance_analysis', 'Performance Analysis'),
        ('custom_script', 'Custom Script'),
    ]
    
    CLOUD_PROVIDERS = [
        ('aws', 'Amazon Web Services'),
        ('azure', 'Microsoft Azure'),
        ('multi', 'Multi-Cloud'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100)
    description = models.TextField()
    template_type = models.CharField(max_length=30, choices=TEMPLATE_TYPES)
    cloud_provider = models.Char<PERSON>ield(max_length=10, choices=CLOUD_PROVIDERS)
    
    # Template configuration
    parameters = models.JSONField(default=dict, help_text="Template parameters schema")
    script_content = models.TextField(blank=True, help_text="Script or automation content")
    
    # Metadata
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='automation_templates')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)
    is_public = models.BooleanField(default=False, help_text="Available to all users")
    
    # Usage tracking
    usage_count = models.IntegerField(default=0)
    last_used = models.DateTimeField(null=True, blank=True)

    class Meta:
        verbose_name = "Automation Template"
        verbose_name_plural = "Automation Templates"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.name} ({self.get_template_type_display()})"

    def increment_usage(self):
        from django.utils import timezone
        self.usage_count += 1
        self.last_used = timezone.now()
        self.save(update_fields=['usage_count', 'last_used'])


class AdHocRun(models.Model):
    """Ad-hoc automation run"""
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('running', 'Running'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    
    # Template or custom
    template = models.ForeignKey(AutomationTemplate, on_delete=models.SET_NULL, null=True, blank=True)
    custom_script = models.TextField(blank=True, help_text="Custom script if not using template")
    
    # Execution details
    parameters = models.JSONField(default=dict, help_text="Runtime parameters")
    target_resources = models.JSONField(default=list, help_text="Target resource IDs")
    
    # Status and timing
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    started_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='adhoc_runs')
    started_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    
    # Results
    output = models.TextField(blank=True)
    error_message = models.TextField(blank=True)
    exit_code = models.IntegerField(null=True, blank=True)
    
    # Metadata
    execution_time = models.FloatField(null=True, blank=True, help_text="Execution time in seconds")
    resources_affected = models.IntegerField(default=0)

    class Meta:
        verbose_name = "Ad-Hoc Run"
        verbose_name_plural = "Ad-Hoc Runs"
        ordering = ['-started_at']

    def __str__(self):
        return f"{self.name} - {self.get_status_display()}"

    @property
    def duration(self):
        if self.started_at and self.completed_at:
            delta = self.completed_at - self.started_at
            return delta.total_seconds()
        return None


class RunStep(models.Model):
    """Individual step in an ad-hoc run"""
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('running', 'Running'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('skipped', 'Skipped'),
    ]

    run = models.ForeignKey(AdHocRun, on_delete=models.CASCADE, related_name='steps')
    step_number = models.IntegerField()
    step_name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    started_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    
    output = models.TextField(blank=True)
    error_message = models.TextField(blank=True)

    class Meta:
        verbose_name = "Run Step"
        verbose_name_plural = "Run Steps"
        ordering = ['step_number']
        unique_together = ['run', 'step_number']

    def __str__(self):
        return f"Step {self.step_number}: {self.step_name}"


# Sample data creation
class OperationsSampleData:
    """Helper class to create sample operations data"""
    
    @staticmethod
    def create_sample_templates():
        from django.contrib.auth.models import User
        
        # Get or create admin user
        admin_user, _ = User.objects.get_or_create(
            username='admin',
            defaults={'email': '<EMAIL>', 'is_staff': True}
        )
        
        templates_data = [
            {
                'name': 'AWS Resource Cleanup',
                'description': 'Identify and clean up unused AWS resources to optimize costs',
                'template_type': 'resource_cleanup',
                'cloud_provider': 'aws',
                'parameters': {
                    'resource_types': ['EC2', 'EBS', 'ELB'],
                    'age_threshold_days': 30,
                    'dry_run': True,
                    'exclude_tags': ['DoNotDelete', 'Production']
                },
                'script_content': '''#!/bin/bash
# AWS Resource Cleanup Script
echo "Starting AWS resource cleanup..."
echo "Resource types: ${RESOURCE_TYPES}"
echo "Age threshold: ${AGE_THRESHOLD_DAYS} days"

if [ "$DRY_RUN" = "true" ]; then
    echo "DRY RUN MODE - No resources will be deleted"
fi

# Add cleanup logic here
echo "Cleanup completed successfully"
'''
            },
            {
                'name': 'Security Compliance Audit',
                'description': 'Comprehensive security audit across cloud resources',
                'template_type': 'security_audit',
                'cloud_provider': 'multi',
                'parameters': {
                    'check_encryption': True,
                    'check_public_access': True,
                    'check_iam_policies': True,
                    'generate_report': True
                },
                'script_content': '''#!/bin/bash
# Security Compliance Audit
echo "Starting security compliance audit..."

# Check encryption status
if [ "$CHECK_ENCRYPTION" = "true" ]; then
    echo "Checking encryption compliance..."
fi

# Check public access
if [ "$CHECK_PUBLIC_ACCESS" = "true" ]; then
    echo "Checking for public access violations..."
fi

echo "Security audit completed"
'''
            },
            {
                'name': 'Cost Optimization Analysis',
                'description': 'Analyze resource usage and identify cost optimization opportunities',
                'template_type': 'cost_optimization',
                'cloud_provider': 'aws',
                'parameters': {
                    'analyze_compute': True,
                    'analyze_storage': True,
                    'analyze_network': True,
                    'recommendation_threshold': 100
                },
                'script_content': '''#!/bin/bash
# Cost Optimization Analysis
echo "Starting cost optimization analysis..."

echo "Analyzing compute resources..."
echo "Analyzing storage resources..."
echo "Analyzing network resources..."

echo "Generating cost optimization recommendations..."
echo "Analysis completed"
'''
            },
            {
                'name': 'Azure VM Performance Check',
                'description': 'Monitor and analyze Azure VM performance metrics',
                'template_type': 'performance_analysis',
                'cloud_provider': 'azure',
                'parameters': {
                    'metrics': ['CPU', 'Memory', 'Disk', 'Network'],
                    'time_range_hours': 24,
                    'alert_threshold': 80
                },
                'script_content': '''#!/bin/bash
# Azure VM Performance Check
echo "Starting Azure VM performance analysis..."

echo "Collecting performance metrics for last ${TIME_RANGE_HOURS} hours"
echo "Alert threshold: ${ALERT_THRESHOLD}%"

echo "Performance analysis completed"
'''
            },
            {
                'name': 'Backup Verification',
                'description': 'Verify backup status and integrity across resources',
                'template_type': 'backup_verification',
                'cloud_provider': 'multi',
                'parameters': {
                    'check_frequency': 'daily',
                    'verify_integrity': True,
                    'alert_on_failure': True
                },
                'script_content': '''#!/bin/bash
# Backup Verification Script
echo "Starting backup verification..."

echo "Checking backup frequency: ${CHECK_FREQUENCY}"
echo "Verify integrity: ${VERIFY_INTEGRITY}"

echo "Backup verification completed"
'''
            }
        ]
        
        created_templates = []
        for template_data in templates_data:
            template, created = AutomationTemplate.objects.get_or_create(
                name=template_data['name'],
                defaults={
                    'description': template_data['description'],
                    'template_type': template_data['template_type'],
                    'cloud_provider': template_data['cloud_provider'],
                    'parameters': template_data['parameters'],
                    'script_content': template_data['script_content'],
                    'created_by': admin_user,
                    'is_public': True
                }
            )
            if created:
                created_templates.append(template)
        
        return f"Created {len(created_templates)} automation templates"
    
    @staticmethod
    def create_sample_runs():
        from django.contrib.auth.models import User
        from django.utils import timezone
        from datetime import timedelta
        import random
        
        admin_user, _ = User.objects.get_or_create(
            username='admin',
            defaults={'email': '<EMAIL>', 'is_staff': True}
        )
        
        templates = AutomationTemplate.objects.all()
        if not templates:
            OperationsSampleData.create_sample_templates()
            templates = AutomationTemplate.objects.all()
        
        runs_data = [
            {
                'name': 'Weekly Resource Cleanup',
                'description': 'Automated weekly cleanup of unused resources',
                'template': templates.filter(template_type='resource_cleanup').first(),
                'status': 'completed',
                'completed_hours_ago': 2
            },
            {
                'name': 'Security Audit - Production',
                'description': 'Monthly security audit for production environment',
                'template': templates.filter(template_type='security_audit').first(),
                'status': 'running',
                'completed_hours_ago': None
            },
            {
                'name': 'Cost Analysis Q4',
                'description': 'Quarterly cost optimization analysis',
                'template': templates.filter(template_type='cost_optimization').first(),
                'status': 'completed',
                'completed_hours_ago': 24
            }
        ]
        
        created_runs = []
        for run_data in runs_data:
            if run_data['completed_hours_ago']:
                completed_at = timezone.now() - timedelta(hours=run_data['completed_hours_ago'])
                started_at = completed_at - timedelta(minutes=random.randint(5, 30))
            else:
                started_at = timezone.now() - timedelta(minutes=random.randint(5, 15))
                completed_at = None
            
            run = AdHocRun.objects.create(
                name=run_data['name'],
                description=run_data['description'],
                template=run_data['template'],
                status=run_data['status'],
                started_by=admin_user,
                started_at=started_at,
                completed_at=completed_at,
                parameters=run_data['template'].parameters if run_data['template'] else {},
                resources_affected=random.randint(5, 50)
            )
            
            # Create sample steps
            steps_data = [
                'Initialize environment',
                'Validate parameters',
                'Execute main task',
                'Generate report',
                'Cleanup'
            ]
            
            for i, step_name in enumerate(steps_data, 1):
                step_status = 'completed' if run.status == 'completed' else ('running' if i <= 3 else 'pending')
                step_started = started_at + timedelta(minutes=i*2) if step_status in ['completed', 'running'] else None
                step_completed = step_started + timedelta(minutes=1) if step_status == 'completed' else None
                
                RunStep.objects.create(
                    run=run,
                    step_number=i,
                    step_name=step_name,
                    status=step_status,
                    started_at=step_started,
                    completed_at=step_completed,
                    output=f"Step {i} completed successfully" if step_status == 'completed' else ""
                )
            
            created_runs.append(run)
        
        return f"Created {len(created_runs)} sample ad-hoc runs"
