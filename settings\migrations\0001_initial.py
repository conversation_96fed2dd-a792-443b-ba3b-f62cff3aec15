# Generated by Django 4.2.7 on 2025-05-29 17:27

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='UserManagement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action_type', models.CharField(choices=[('create', 'User Created'), ('update', 'User Updated'), ('delete', 'User Deleted'), ('activate', 'User Activated'), ('deactivate', 'User Deactivated'), ('group_change', 'Group Changed'), ('permission_change', 'Permission Changed')], max_length=20)),
                ('action_details', models.TextField()),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('performed_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='performed_actions', to=settings.AUTH_USER_MODEL)),
                ('target_user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='management_logs', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'User Management Log',
                'verbose_name_plural': 'User Management Logs',
                'ordering': ['-timestamp'],
            },
        ),
        migrations.CreateModel(
            name='AzureSession',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True)),
                ('auth_method', models.CharField(choices=[('service_principal', 'Service Principal'), ('managed_identity', 'Managed Identity'), ('azure_cli', 'Azure CLI'), ('device_code', 'Device Code')], max_length=20)),
                ('tenant_id', models.CharField(blank=True, max_length=100)),
                ('client_id', models.CharField(blank=True, max_length=100)),
                ('client_secret', models.CharField(blank=True, max_length=200)),
                ('subscription_id', models.CharField(blank=True, max_length=100)),
                ('subscription_name', models.CharField(blank=True, max_length=100)),
                ('default_resource_group', models.CharField(blank=True, max_length=100)),
                ('default_location', models.CharField(blank=True, max_length=50)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('last_tested', models.DateTimeField(blank=True, null=True)),
                ('test_status', models.CharField(default='untested', max_length=20)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='azure_sessions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Azure Session',
                'verbose_name_plural': 'Azure Sessions',
            },
        ),
        migrations.CreateModel(
            name='AppConfiguration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('key', models.CharField(max_length=100, unique=True)),
                ('value', models.TextField()),
                ('config_type', models.CharField(choices=[('string', 'String'), ('integer', 'Integer'), ('boolean', 'Boolean'), ('json', 'JSON'), ('text', 'Text')], default='string', max_length=10)),
                ('description', models.TextField(blank=True)),
                ('category', models.CharField(default='general', max_length=50)),
                ('is_sensitive', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'App Configuration',
                'verbose_name_plural': 'App Configurations',
                'ordering': ['category', 'key'],
            },
        ),
    ]
