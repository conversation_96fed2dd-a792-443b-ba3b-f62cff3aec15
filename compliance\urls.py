from django.urls import path
from . import views

app_name = 'compliance'

urlpatterns = [
    # Patch Calendar
    path('patch-calendar/', views.PatchCalendarListView.as_view(), name='patch_calendar_list'),
    path('patch-calendar/create/', views.PatchCalendarCreateView.as_view(), name='patch_calendar_create'),
    path('patch-calendar/<uuid:pk>/', views.PatchCalendarDetailView.as_view(), name='patch_calendar_detail'),
    path('patch-calendar/<uuid:pk>/edit/', views.PatchCalendarEditView.as_view(), name='patch_calendar_edit'),
    path('patch-calendar/<uuid:pk>/delete/', views.PatchCalendarDeleteView.as_view(), name='patch_calendar_delete'),
    
    # EKS Node Refresh
    path('eks-refresh/', views.EKSNodeRefreshListView.as_view(), name='eks_refresh_list'),
    path('eks-refresh/create/', views.EKSNodeRefreshCreateView.as_view(), name='eks_refresh_create'),
    path('eks-refresh/<uuid:pk>/', views.EKSNodeRefreshDetailView.as_view(), name='eks_refresh_detail'),
    path('eks-refresh/<uuid:pk>/edit/', views.EKSNodeRefreshEditView.as_view(), name='eks_refresh_edit'),
    path('eks-refresh/<uuid:pk>/delete/', views.EKSNodeRefreshDeleteView.as_view(), name='eks_refresh_delete'),
    path('eks-refresh/<uuid:pk>/execute/', views.EKSNodeRefreshExecuteView.as_view(), name='eks_refresh_execute'),
    
    # AKS Node Refresh
    path('aks-refresh/', views.AKSNodeRefreshListView.as_view(), name='aks_refresh_list'),
    path('aks-refresh/create/', views.AKSNodeRefreshCreateView.as_view(), name='aks_refresh_create'),
    path('aks-refresh/<uuid:pk>/', views.AKSNodeRefreshDetailView.as_view(), name='aks_refresh_detail'),
    path('aks-refresh/<uuid:pk>/edit/', views.AKSNodeRefreshEditView.as_view(), name='aks_refresh_edit'),
    path('aks-refresh/<uuid:pk>/delete/', views.AKSNodeRefreshDeleteView.as_view(), name='aks_refresh_delete'),
    path('aks-refresh/<uuid:pk>/execute/', views.AKSNodeRefreshExecuteView.as_view(), name='aks_refresh_execute'),
    
    # API endpoints
    path('api/patch-calendar/', views.PatchCalendarAPIView.as_view(), name='api_patch_calendar'),
    path('api/eks-refresh/', views.EKSRefreshAPIView.as_view(), name='api_eks_refresh'),
    path('api/aks-refresh/', views.AKSRefreshAPIView.as_view(), name='api_aks_refresh'),
]
