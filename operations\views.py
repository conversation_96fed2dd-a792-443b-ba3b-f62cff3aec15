from django.shortcuts import render, get_object_or_404, redirect
from django.views.generic import List<PERSON>iew, DetailView, CreateView, TemplateView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.http import JsonResponse
from django.contrib import messages
from django.urls import reverse_lazy
from django.db.models import Q, Count
from django.utils import timezone

from .models import AutomationTemplate, AdHocRun, RunStep


class OperationsDashboardView(LoginRequiredMixin, TemplateView):
    template_name = 'operations/dashboard.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Template statistics
        context['template_count'] = AutomationTemplate.objects.filter(is_active=True).count()
        context['public_template_count'] = AutomationTemplate.objects.filter(is_active=True, is_public=True).count()
        
        # Run statistics
        context['total_runs'] = AdHocRun.objects.count()
        context['running_runs'] = AdHocRun.objects.filter(status='running').count()
        context['completed_runs'] = AdHocRun.objects.filter(status='completed').count()
        context['failed_runs'] = AdHocRun.objects.filter(status='failed').count()
        
        # Recent runs
        context['recent_runs'] = AdHocRun.objects.select_related(
            'template', 'started_by'
        ).order_by('-started_at')[:10]
        
        # Popular templates
        context['popular_templates'] = AutomationTemplate.objects.filter(
            is_active=True
        ).order_by('-usage_count')[:5]
        
        # Template type distribution
        template_types = AutomationTemplate.objects.filter(
            is_active=True
        ).values('template_type').annotate(count=Count('id'))
        context['template_type_distribution'] = {item['template_type']: item['count'] for item in template_types}
        
        return context


class AutomationTemplateListView(LoginRequiredMixin, ListView):
    model = AutomationTemplate
    template_name = 'operations/template_list.html'
    context_object_name = 'templates'
    paginate_by = 20

    def get_queryset(self):
        queryset = AutomationTemplate.objects.filter(is_active=True).select_related('created_by')
        
        # Filter by template type
        template_type = self.request.GET.get('type')
        if template_type:
            queryset = queryset.filter(template_type=template_type)
        
        # Filter by cloud provider
        cloud_provider = self.request.GET.get('provider')
        if cloud_provider:
            queryset = queryset.filter(cloud_provider=cloud_provider)
        
        # Search
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) | Q(description__icontains=search)
            )
        
        # Show only public templates or user's own templates
        if not self.request.user.is_staff:
            queryset = queryset.filter(
                Q(is_public=True) | Q(created_by=self.request.user)
            )
        
        return queryset.order_by('-created_at')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['template_types'] = AutomationTemplate.TEMPLATE_TYPES
        context['cloud_providers'] = AutomationTemplate.CLOUD_PROVIDERS
        context['current_filters'] = {
            'type': self.request.GET.get('type', ''),
            'provider': self.request.GET.get('provider', ''),
            'search': self.request.GET.get('search', ''),
        }
        return context


class AutomationTemplateDetailView(LoginRequiredMixin, DetailView):
    model = AutomationTemplate
    template_name = 'operations/template_detail.html'
    context_object_name = 'template'

    def get_queryset(self):
        queryset = AutomationTemplate.objects.filter(is_active=True).select_related('created_by')
        
        # Show only public templates or user's own templates
        if not self.request.user.is_staff:
            queryset = queryset.filter(
                Q(is_public=True) | Q(created_by=self.request.user)
            )
        
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        template = self.get_object()
        
        # Recent runs using this template
        context['recent_runs'] = AdHocRun.objects.filter(
            template=template
        ).select_related('started_by').order_by('-started_at')[:5]
        
        return context


class AdHocRunListView(LoginRequiredMixin, ListView):
    model = AdHocRun
    template_name = 'operations/adhoc_list.html'
    context_object_name = 'runs'
    paginate_by = 20

    def get_queryset(self):
        queryset = AdHocRun.objects.select_related('template', 'started_by')
        
        # Filter by status
        status = self.request.GET.get('status')
        if status:
            queryset = queryset.filter(status=status)
        
        # Filter by template
        template_id = self.request.GET.get('template')
        if template_id:
            queryset = queryset.filter(template_id=template_id)
        
        # Search
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) | Q(description__icontains=search)
            )
        
        # Show only user's own runs unless staff
        if not self.request.user.is_staff:
            queryset = queryset.filter(started_by=self.request.user)
        
        return queryset.order_by('-started_at')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['status_choices'] = AdHocRun.STATUS_CHOICES
        context['templates'] = AutomationTemplate.objects.filter(is_active=True)
        context['current_filters'] = {
            'status': self.request.GET.get('status', ''),
            'template': self.request.GET.get('template', ''),
            'search': self.request.GET.get('search', ''),
        }
        return context


class AdHocRunDetailView(LoginRequiredMixin, DetailView):
    model = AdHocRun
    template_name = 'operations/adhoc_detail.html'
    context_object_name = 'run'

    def get_queryset(self):
        queryset = AdHocRun.objects.select_related('template', 'started_by').prefetch_related('steps')
        
        # Show only user's own runs unless staff
        if not self.request.user.is_staff:
            queryset = queryset.filter(started_by=self.request.user)
        
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        run = self.get_object()
        
        # Get run steps
        context['steps'] = run.steps.order_by('step_number')
        
        return context


class AdHocRunCreateView(LoginRequiredMixin, TemplateView):
    template_name = 'operations/adhoc_create.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get available templates
        templates = AutomationTemplate.objects.filter(is_active=True)
        if not self.request.user.is_staff:
            templates = templates.filter(
                Q(is_public=True) | Q(created_by=self.request.user)
            )
        
        context['templates'] = templates
        
        # If template_id is provided, get the template
        template_id = self.request.GET.get('template')
        if template_id:
            try:
                context['selected_template'] = templates.get(id=template_id)
            except AutomationTemplate.DoesNotExist:
                pass
        
        return context

    def post(self, request, *args, **kwargs):
        try:
            name = request.POST.get('name')
            description = request.POST.get('description', '')
            template_id = request.POST.get('template')
            custom_script = request.POST.get('custom_script', '')
            
            # Validate required fields
            if not name:
                messages.error(request, 'Run name is required.')
                return self.get(request, *args, **kwargs)
            
            # Get template if specified
            template = None
            if template_id:
                try:
                    template = AutomationTemplate.objects.get(id=template_id, is_active=True)
                    # Increment usage count
                    template.increment_usage()
                except AutomationTemplate.DoesNotExist:
                    messages.error(request, 'Selected template not found.')
                    return self.get(request, *args, **kwargs)
            
            # Validate that either template or custom script is provided
            if not template and not custom_script:
                messages.error(request, 'Either select a template or provide a custom script.')
                return self.get(request, *args, **kwargs)
            
            # Create the ad-hoc run
            run = AdHocRun.objects.create(
                name=name,
                description=description,
                template=template,
                custom_script=custom_script,
                started_by=request.user,
                status='pending',
                parameters=template.parameters if template else {}
            )
            
            # Create initial steps
            if template:
                steps = [
                    'Initialize environment',
                    'Validate parameters',
                    'Execute automation',
                    'Generate results',
                    'Cleanup'
                ]
            else:
                steps = [
                    'Initialize environment',
                    'Execute custom script',
                    'Cleanup'
                ]
            
            for i, step_name in enumerate(steps, 1):
                RunStep.objects.create(
                    run=run,
                    step_number=i,
                    step_name=step_name,
                    status='pending'
                )
            
            # Simulate starting the run (in production, this would trigger actual execution)
            run.status = 'running'
            run.save()
            
            # Start first step
            first_step = run.steps.first()
            if first_step:
                first_step.status = 'running'
                first_step.started_at = timezone.now()
                first_step.save()
            
            messages.success(request, f'Ad-hoc run "{name}" started successfully!')
            return redirect('operations:adhoc_detail', pk=run.pk)
            
        except Exception as e:
            messages.error(request, f'Error creating ad-hoc run: {str(e)}')
            return self.get(request, *args, **kwargs)


class RunTemplateView(LoginRequiredMixin, TemplateView):
    """Quick run a template"""
    
    def post(self, request, template_id):
        try:
            template = get_object_or_404(AutomationTemplate, id=template_id, is_active=True)
            
            # Check permissions
            if not template.is_public and template.created_by != request.user and not request.user.is_staff:
                messages.error(request, 'You do not have permission to run this template.')
                return redirect('operations:template_detail', pk=template_id)
            
            # Create quick run
            run = AdHocRun.objects.create(
                name=f"Quick run: {template.name}",
                description=f"Quick execution of {template.name}",
                template=template,
                started_by=request.user,
                status='running',
                parameters=template.parameters
            )
            
            # Increment template usage
            template.increment_usage()
            
            # Create steps
            steps = [
                'Initialize environment',
                'Execute template',
                'Generate results'
            ]
            
            for i, step_name in enumerate(steps, 1):
                RunStep.objects.create(
                    run=run,
                    step_number=i,
                    step_name=step_name,
                    status='pending' if i > 1 else 'running'
                )
            
            messages.success(request, f'Template "{template.name}" is now running!')
            return redirect('operations:adhoc_detail', pk=run.pk)
            
        except Exception as e:
            messages.error(request, f'Error running template: {str(e)}')
            return redirect('operations:template_detail', pk=template_id)


# API Views
class TemplateListAPIView(LoginRequiredMixin, TemplateView):
    def get(self, request, *args, **kwargs):
        templates = AutomationTemplate.objects.filter(is_active=True)
        
        # Apply filters
        template_type = request.GET.get('type')
        if template_type:
            templates = templates.filter(template_type=template_type)
        
        cloud_provider = request.GET.get('provider')
        if cloud_provider:
            templates = templates.filter(cloud_provider=cloud_provider)
        
        # Show only public templates or user's own templates
        if not request.user.is_staff:
            templates = templates.filter(
                Q(is_public=True) | Q(created_by=request.user)
            )
        
        data = {
            'templates': [
                {
                    'id': str(template.id),
                    'name': template.name,
                    'description': template.description,
                    'template_type': template.template_type,
                    'cloud_provider': template.cloud_provider,
                    'usage_count': template.usage_count,
                    'created_by': template.created_by.username,
                    'created_at': template.created_at.isoformat(),
                }
                for template in templates
            ]
        }
        
        return JsonResponse(data)


class RunStatusAPIView(LoginRequiredMixin, TemplateView):
    def get(self, request, run_id):
        try:
            run = AdHocRun.objects.get(id=run_id)
            
            # Check permissions
            if not request.user.is_staff and run.started_by != request.user:
                return JsonResponse({'error': 'Permission denied'}, status=403)
            
            data = {
                'id': str(run.id),
                'name': run.name,
                'status': run.status,
                'started_at': run.started_at.isoformat(),
                'completed_at': run.completed_at.isoformat() if run.completed_at else None,
                'duration': run.duration,
                'resources_affected': run.resources_affected,
                'steps': [
                    {
                        'step_number': step.step_number,
                        'step_name': step.step_name,
                        'status': step.status,
                        'started_at': step.started_at.isoformat() if step.started_at else None,
                        'completed_at': step.completed_at.isoformat() if step.completed_at else None,
                    }
                    for step in run.steps.order_by('step_number')
                ]
            }
            
            return JsonResponse(data)
            
        except AdHocRun.DoesNotExist:
            return JsonResponse({'error': 'Run not found'}, status=404)
