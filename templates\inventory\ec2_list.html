{% extends 'base.html' %}
{% load static %}

{% block title %}EC2 Instances - Cloud Central SS{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'inventory:dashboard' %}">Dashboard</a></li>
        <li class="breadcrumb-item"><a href="{% url 'inventory:resource_list' %}">Resources</a></li>
        <li class="breadcrumb-item active" aria-current="page">EC2 Instances</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2><i class="fas fa-desktop me-2"></i>EC2 Instances</h2>
                    <p class="text-muted mb-0">Manage and monitor AWS EC2 instances across all accounts</p>
                </div>
                <div class="btn-group">
                    <a href="{% url 'inventory:resource_export' %}?format=csv&type=EC2" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-download me-1"></i>Export CSV
                    </a>
                    <a href="{% url 'inventory:resource_export' %}?format=json&type=EC2" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-download me-1"></i>Export JSON
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters Section (Top of Page) -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-filter me-2"></i>Filters & Search
                    </h6>
                </div>
                <div class="card-body">
                    <form method="get" id="filter-form">
                        <div class="row g-3">
                            <!-- Search -->
                            <div class="col-md-3">
                                <label class="form-label">Search</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" name="search" 
                                           value="{{ current_filters.search }}" 
                                           placeholder="Search instances..." id="resource-search">
                                    <button class="btn btn-outline-secondary" type="submit">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <!-- Business Unit -->
                            <div class="col-md-2">
                                <label class="form-label">Business Unit</label>
                                <select class="form-select" name="business_unit">
                                    <option value="">All BUs</option>
                                    {% for bu in business_units %}
                                    <option value="{{ bu.code }}" {% if current_filters.business_unit == bu.code %}selected{% endif %}>
                                        {{ bu.code }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                            
                            <!-- AWS Account -->
                            <div class="col-md-2">
                                <label class="form-label">Account</label>
                                <select class="form-select" name="account">
                                    <option value="">All Accounts</option>
                                    {% for account in aws_accounts %}
                                    <option value="{{ account.id }}" {% if current_filters.account == account.id|stringformat:"s" %}selected{% endif %}>
                                        {{ account.account_name }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                            
                            <!-- Region -->
                            <div class="col-md-2">
                                <label class="form-label">Region</label>
                                <select class="form-select" name="region">
                                    <option value="">All Regions</option>
                                    {% for region in regions %}
                                    <option value="{{ region }}" {% if current_filters.region == region %}selected{% endif %}>
                                        {{ region }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                            
                            <!-- State -->
                            <div class="col-md-2">
                                <label class="form-label">State</label>
                                <select class="form-select" name="state">
                                    <option value="">All States</option>
                                    {% for state in states %}
                                    <option value="{{ state }}" {% if current_filters.state == state %}selected{% endif %}>
                                        {{ state|title }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                            
                            <!-- Instance Type -->
                            <div class="col-md-1">
                                <label class="form-label">Type</label>
                                <select class="form-select" name="instance_type">
                                    <option value="">All Types</option>
                                    {% for itype in instance_types %}
                                    <option value="{{ itype }}" {% if current_filters.instance_type == itype %}selected{% endif %}>
                                        {{ itype }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        
                        <div class="row mt-3">
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="fas fa-search me-1"></i>Apply Filters
                                </button>
                                <a href="{% url 'inventory:resource_type_list' 'ec2' %}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-1"></i>Clear Filters
                                </a>
                                <span class="ms-3 text-muted">
                                    Showing {{ resources|length }} of {{ paginator.count }} instances
                                </span>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- EC2 Instances Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-vernova">
                                <tr>
                                    <th>BU</th>
                                    <th>Account Name</th>
                                    <th>Account ID</th>
                                    <th>Region</th>
                                    <th>Instance ID</th>
                                    <th>State</th>
                                    <th>Name</th>
                                    <th>OS Information</th>
                                    <th>Private IP Address</th>
                                    <th>Instance Type</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for instance in resources %}
                                <tr>
                                    <td>
                                        <span class="badge bg-info">{{ instance.aws_account.business_unit.code }}</span>
                                    </td>
                                    <td>{{ instance.aws_account.account_name }}</td>
                                    <td><code>{{ instance.aws_account.account_id }}</code></td>
                                    <td>{{ instance.region }}</td>
                                    <td>
                                        <code>{{ instance.resource_id }}</code>
                                    </td>
                                    <td>
                                        <span class="badge badge-{{ instance.state|lower }}">{{ instance.state|title }}</span>
                                    </td>
                                    <td>
                                        <strong>{{ instance.resource_name|default:"N/A" }}</strong>
                                    </td>
                                    <td>
                                        {% if instance.ec2instance.platform %}
                                            {{ instance.ec2instance.platform }}
                                            {% if instance.ec2instance.os_version %}
                                                <br><small class="text-muted">{{ instance.ec2instance.os_version }}</small>
                                            {% endif %}
                                        {% else %}
                                            <span class="text-muted">Unknown</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if instance.ec2instance.private_ip %}
                                            <code>{{ instance.ec2instance.private_ip }}</code>
                                        {% else %}
                                            <span class="text-muted">N/A</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">{{ instance.ec2instance.instance_type }}</span>
                                    </td>
                                    <td>
                                        <a href="{% url 'inventory:resource_detail' instance.pk %}" 
                                           class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="11" class="text-center text-muted py-4">
                                        <i class="fas fa-search fa-2x mb-2"></i>
                                        <p>No EC2 instances found matching your criteria.</p>
                                        <a href="{% url 'inventory:resource_type_list' 'ec2' %}" class="btn btn-outline-primary">
                                            Clear Filters
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    {% if is_paginated %}
                    <nav aria-label="EC2 pagination" class="mt-4">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">
                                    <i class="fas fa-angle-double-left"></i>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">
                                    <i class="fas fa-angle-left"></i>
                                </a>
                            </li>
                            {% endif %}
                            
                            <li class="page-item active">
                                <span class="page-link">
                                    Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                                </span>
                            </li>
                            
                            {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">
                                    <i class="fas fa-angle-right"></i>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">
                                    <i class="fas fa-angle-double-right"></i>
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Auto-submit form on filter change
    $('#filter-form select').on('change', function() {
        $('#filter-form').submit();
    });
    
    // Search with delay
    let searchTimeout;
    $('#resource-search').on('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            $('#filter-form').submit();
        }, 500);
    });
});
</script>
{% endblock %}
