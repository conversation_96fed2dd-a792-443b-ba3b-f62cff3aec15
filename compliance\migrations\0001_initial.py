# Generated by Django 4.2.7 on 2025-05-29 17:27

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('inventory', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='PatchCalendar',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('patch_type', models.CharField(choices=[('security', 'Security Patch'), ('critical', 'Critical Update'), ('feature', 'Feature Update'), ('maintenance', 'Maintenance')], max_length=20)),
                ('scheduled_date', models.DateTimeField()),
                ('maintenance_window_start', models.DateTimeField()),
                ('maintenance_window_end', models.DateTimeField()),
                ('resource_types', models.J<PERSON><PERSON>ield(default=list)),
                ('status', models.CharField(choices=[('scheduled', 'Scheduled'), ('in_progress', 'In Progress'), ('completed', 'Completed'), ('failed', 'Failed'), ('postponed', 'Postponed')], default='scheduled', max_length=20)),
                ('progress_percentage', models.IntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('aws_accounts', models.ManyToManyField(blank=True, to='inventory.awsaccount')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Patch Calendar Entry',
                'verbose_name_plural': 'Patch Calendar Entries',
                'ordering': ['scheduled_date'],
            },
        ),
        migrations.CreateModel(
            name='EKSNodeRefresh',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('cluster_name', models.CharField(max_length=100)),
                ('region', models.CharField(max_length=20)),
                ('refresh_type', models.CharField(choices=[('ami_update', 'AMI Update'), ('kubernetes_version', 'Kubernetes Version Upgrade'), ('security_patch', 'Security Patch'), ('scheduled_maintenance', 'Scheduled Maintenance')], max_length=30)),
                ('current_ami', models.CharField(blank=True, max_length=100)),
                ('target_ami', models.CharField(blank=True, max_length=100)),
                ('current_k8s_version', models.CharField(blank=True, max_length=20)),
                ('target_k8s_version', models.CharField(blank=True, max_length=20)),
                ('scheduled_date', models.DateTimeField()),
                ('estimated_duration', models.IntegerField(help_text='Duration in minutes')),
                ('status', models.CharField(choices=[('planned', 'Planned'), ('scheduled', 'Scheduled'), ('in_progress', 'In Progress'), ('completed', 'Completed'), ('failed', 'Failed'), ('rolled_back', 'Rolled Back')], default='planned', max_length=20)),
                ('progress_percentage', models.IntegerField(default=0)),
                ('started_at', models.DateTimeField(blank=True, null=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('success_count', models.IntegerField(default=0)),
                ('failure_count', models.IntegerField(default=0)),
                ('rollback_required', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('aws_account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='inventory.awsaccount')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'EKS Node Refresh',
                'verbose_name_plural': 'EKS Node Refreshes',
                'ordering': ['-scheduled_date'],
            },
        ),
        migrations.CreateModel(
            name='AKSNodeRefresh',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('cluster_name', models.CharField(max_length=100)),
                ('subscription_id', models.CharField(max_length=100)),
                ('resource_group', models.CharField(max_length=100)),
                ('refresh_type', models.CharField(choices=[('node_image_upgrade', 'Node Image Upgrade'), ('kubernetes_version', 'Kubernetes Version Upgrade'), ('security_patch', 'Security Patch'), ('scheduled_maintenance', 'Scheduled Maintenance')], max_length=30)),
                ('current_node_image', models.CharField(blank=True, max_length=100)),
                ('target_node_image', models.CharField(blank=True, max_length=100)),
                ('current_k8s_version', models.CharField(blank=True, max_length=20)),
                ('target_k8s_version', models.CharField(blank=True, max_length=20)),
                ('scheduled_date', models.DateTimeField()),
                ('estimated_duration', models.IntegerField(help_text='Duration in minutes')),
                ('status', models.CharField(choices=[('planned', 'Planned'), ('scheduled', 'Scheduled'), ('in_progress', 'In Progress'), ('completed', 'Completed'), ('failed', 'Failed'), ('rolled_back', 'Rolled Back')], default='planned', max_length=20)),
                ('progress_percentage', models.IntegerField(default=0)),
                ('started_at', models.DateTimeField(blank=True, null=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('success_count', models.IntegerField(default=0)),
                ('failure_count', models.IntegerField(default=0)),
                ('rollback_required', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'AKS Node Refresh',
                'verbose_name_plural': 'AKS Node Refreshes',
                'ordering': ['-scheduled_date'],
            },
        ),
    ]
