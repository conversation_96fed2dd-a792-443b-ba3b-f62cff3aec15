from django.shortcuts import render, get_object_or_404
from django.views.generic import ListView, DetailView, TemplateView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.http import JsonResponse, HttpResponse
from django.db.models import Q, Count
from django.core.paginator import Paginator
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
import json
import csv

from .models import (
    BaseResource, EC2Instance, S3Bucket, EKSCluster,
    ECSCluster, ECRRepository, LambdaFunction,
    BusinessUnit, AWSAccount, Tag
)


class DashboardView(LoginRequiredMixin, TemplateView):
    template_name = 'inventory/dashboard.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Get resource counts by type
        resource_counts = {
            'total': BaseResource.objects.count(),
            'ec2': EC2Instance.objects.count(),
            's3': S3Bucket.objects.count(),
            'eks': EKSCluster.objects.count(),
            'ecs': ECSCluster.objects.count(),
            'ecr': ECRRepository.objects.count(),
            'lambda': LambdaFunction.objects.count(),
        }

        # Get account counts by business unit
        bu_counts = BusinessUnit.objects.annotate(
            account_count=Count('aws_accounts')
        ).values('code', 'name', 'account_count')

        # Get recent resources (last 10)
        recent_resources = BaseResource.objects.select_related(
            'aws_account', 'aws_account__business_unit'
        ).order_by('-last_updated')[:10]

        # Get resource distribution by region
        region_distribution = BaseResource.objects.values('region').annotate(
            count=Count('id')
        ).order_by('-count')[:10]

        context.update({
            'resource_counts': resource_counts,
            'bu_counts': bu_counts,
            'recent_resources': recent_resources,
            'region_distribution': region_distribution,
        })

        return context


class ResourceListView(LoginRequiredMixin, ListView):
    model = BaseResource
    template_name = 'inventory/resource_list.html'
    context_object_name = 'resources'
    paginate_by = 50

    def get_queryset(self):
        queryset = BaseResource.objects.select_related(
            'aws_account', 'aws_account__business_unit'
        ).prefetch_related('tags')

        # Apply filters
        resource_type = self.request.GET.get('type')
        if resource_type:
            queryset = queryset.filter(resource_type=resource_type)

        account_id = self.request.GET.get('account')
        if account_id:
            queryset = queryset.filter(aws_account_id=account_id)

        region = self.request.GET.get('region')
        if region:
            queryset = queryset.filter(region=region)

        state = self.request.GET.get('state')
        if state:
            queryset = queryset.filter(state=state)

        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(resource_name__icontains=search) |
                Q(resource_id__icontains=search) |
                Q(arn__icontains=search)
            )

        return queryset.order_by('-last_updated')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Get filter options
        context['resource_types'] = BaseResource.RESOURCE_TYPES
        context['aws_accounts'] = AWSAccount.objects.select_related('business_unit')
        context['regions'] = BaseResource.objects.values_list('region', flat=True).distinct()
        context['states'] = BaseResource.objects.values_list('state', flat=True).distinct()

        # Current filters
        context['current_filters'] = {
            'type': self.request.GET.get('type', ''),
            'account': self.request.GET.get('account', ''),
            'region': self.request.GET.get('region', ''),
            'state': self.request.GET.get('state', ''),
            'search': self.request.GET.get('search', ''),
        }

        return context


class ResourceTypeListView(ResourceListView):
    def get_template_names(self):
        resource_type = self.kwargs.get('resource_type', '').lower()
        if resource_type == 'ec2':
            return ['inventory/ec2_list.html']
        else:
            return ['inventory/resource_list.html']

    def get_queryset(self):
        resource_type = self.kwargs.get('resource_type', '').upper()

        if resource_type == 'EC2':
            # For EC2, get EC2Instance objects with related data
            queryset = EC2Instance.objects.select_related(
                'aws_account', 'aws_account__business_unit'
            ).prefetch_related('tags')
        else:
            queryset = BaseResource.objects.select_related(
                'aws_account', 'aws_account__business_unit'
            ).prefetch_related('tags')
            queryset = queryset.filter(resource_type=resource_type)

        # Apply filters from parent class
        account_id = self.request.GET.get('account')
        if account_id:
            queryset = queryset.filter(aws_account_id=account_id)

        region = self.request.GET.get('region')
        if region:
            queryset = queryset.filter(region=region)

        state = self.request.GET.get('state')
        if state:
            queryset = queryset.filter(state=state)

        # Business Unit filter (for EC2)
        business_unit = self.request.GET.get('business_unit')
        if business_unit:
            queryset = queryset.filter(aws_account__business_unit__code=business_unit)

        # Instance Type filter (for EC2)
        instance_type = self.request.GET.get('instance_type')
        if instance_type and resource_type == 'EC2':
            queryset = queryset.filter(instance_type=instance_type)

        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(resource_name__icontains=search) |
                Q(resource_id__icontains=search) |
                Q(arn__icontains=search)
            )

        return queryset.order_by('-last_updated')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        resource_type = self.kwargs.get('resource_type', '').upper()
        context['resource_type'] = resource_type

        # Add EC2-specific context
        if resource_type == 'EC2':
            context['business_units'] = BusinessUnit.objects.all()
            context['instance_types'] = EC2Instance.objects.values_list('instance_type', flat=True).distinct()

            # Update current filters for EC2
            context['current_filters'].update({
                'business_unit': self.request.GET.get('business_unit', ''),
                'instance_type': self.request.GET.get('instance_type', ''),
            })

        return context


class ResourceDetailView(LoginRequiredMixin, DetailView):
    model = BaseResource
    template_name = 'inventory/resource_detail.html'
    context_object_name = 'resource'

    def get_queryset(self):
        return BaseResource.objects.select_related(
            'aws_account', 'aws_account__business_unit'
        ).prefetch_related('tags')


class ResourceSearchView(LoginRequiredMixin, TemplateView):
    template_name = 'inventory/resource_search.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Advanced search form options
        context['resource_types'] = BaseResource.RESOURCE_TYPES
        context['business_units'] = BusinessUnit.objects.all()
        context['aws_accounts'] = AWSAccount.objects.select_related('business_unit')

        return context


class ResourceFilterView(LoginRequiredMixin, TemplateView):
    template_name = 'inventory/resource_filter.html'


class ResourceExportView(LoginRequiredMixin, TemplateView):
    def get(self, request, *args, **kwargs):
        format_type = request.GET.get('format', 'csv')

        # Get filtered queryset
        queryset = BaseResource.objects.select_related(
            'aws_account', 'aws_account__business_unit'
        ).prefetch_related('tags')

        # Apply same filters as ResourceListView
        resource_type = request.GET.get('type')
        if resource_type:
            queryset = queryset.filter(resource_type=resource_type)

        account_id = request.GET.get('account')
        if account_id:
            queryset = queryset.filter(aws_account_id=account_id)

        region = request.GET.get('region')
        if region:
            queryset = queryset.filter(region=region)

        state = request.GET.get('state')
        if state:
            queryset = queryset.filter(state=state)

        search = request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(resource_name__icontains=search) |
                Q(resource_id__icontains=search) |
                Q(arn__icontains=search)
            )

        if format_type == 'csv':
            return self.export_csv(queryset)
        elif format_type == 'json':
            return self.export_json(queryset)

        return HttpResponse("Unsupported format", status=400)

    def export_csv(self, queryset):
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="resources.csv"'

        writer = csv.writer(response)
        writer.writerow([
            'Resource Name', 'Resource Type', 'Resource ID', 'Account',
            'Business Unit', 'Region', 'State', 'ARN', 'Last Updated'
        ])

        for resource in queryset:
            writer.writerow([
                resource.resource_name,
                resource.resource_type,
                resource.resource_id,
                resource.aws_account.account_name,
                resource.aws_account.business_unit.name,
                resource.region,
                resource.state,
                resource.arn,
                resource.last_updated.strftime('%Y-%m-%d %H:%M:%S')
            ])

        return response

    def export_json(self, queryset):
        data = []
        for resource in queryset:
            data.append({
                'resource_name': resource.resource_name,
                'resource_type': resource.resource_type,
                'resource_id': resource.resource_id,
                'account': resource.aws_account.account_name,
                'business_unit': resource.aws_account.business_unit.name,
                'region': resource.region,
                'state': resource.state,
                'arn': resource.arn,
                'last_updated': resource.last_updated.isoformat(),
                'tags': [{'key': tag.key, 'value': tag.value} for tag in resource.tags.all()]
            })

        response = HttpResponse(json.dumps(data, indent=2), content_type='application/json')
        response['Content-Disposition'] = 'attachment; filename="resources.json"'
        return response


# API Views
class ResourceListAPIView(LoginRequiredMixin, TemplateView):
    def get(self, request, *args, **kwargs):
        queryset = BaseResource.objects.select_related(
            'aws_account', 'aws_account__business_unit'
        ).prefetch_related('tags')

        # Apply filters (same as ResourceListView)
        resource_type = request.GET.get('type')
        if resource_type:
            queryset = queryset.filter(resource_type=resource_type)

        account_id = request.GET.get('account')
        if account_id:
            queryset = queryset.filter(aws_account_id=account_id)

        region = request.GET.get('region')
        if region:
            queryset = queryset.filter(region=region)

        state = request.GET.get('state')
        if state:
            queryset = queryset.filter(state=state)

        search = request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(resource_name__icontains=search) |
                Q(resource_id__icontains=search) |
                Q(arn__icontains=search)
            )

        queryset = queryset.order_by('-last_updated')

        # Pagination
        page = request.GET.get('page', 1)
        paginator = Paginator(queryset, 50)
        resources = paginator.get_page(page)

        data = {
            'count': paginator.count,
            'num_pages': paginator.num_pages,
            'current_page': resources.number,
            'results': [
                {
                    'id': resource.id,
                    'resource_name': resource.resource_name,
                    'resource_type': resource.resource_type,
                    'account': resource.aws_account.account_name,
                    'region': resource.region,
                    'state': resource.state,
                    'last_updated': resource.last_updated.isoformat(),
                }
                for resource in resources
            ]
        }

        return JsonResponse(data)


class ResourceDetailAPIView(LoginRequiredMixin, TemplateView):
    def get(self, request, pk, *args, **kwargs):
        resource = get_object_or_404(BaseResource, pk=pk)

        data = {
            'id': resource.id,
            'resource_name': resource.resource_name,
            'resource_type': resource.resource_type,
            'resource_id': resource.resource_id,
            'arn': resource.arn,
            'state': resource.state,
            'region': resource.region,
            'account': {
                'id': resource.aws_account.id,
                'name': resource.aws_account.account_name,
                'account_id': resource.aws_account.account_id,
                'business_unit': resource.aws_account.business_unit.name,
            },
            'tags': [
                {'key': tag.key, 'value': tag.value}
                for tag in resource.tags.all()
            ],
            'created_date': resource.created_date.isoformat() if resource.created_date else None,
            'last_updated': resource.last_updated.isoformat(),
        }

        return JsonResponse(data)


class ResourceSearchAPIView(LoginRequiredMixin, TemplateView):
    def get(self, request, *args, **kwargs):
        query = request.GET.get('q', '')

        if not query:
            return JsonResponse({'results': []})

        resources = BaseResource.objects.filter(
            Q(resource_name__icontains=query) |
            Q(resource_id__icontains=query) |
            Q(arn__icontains=query)
        ).select_related('aws_account')[:20]

        data = {
            'results': [
                {
                    'id': resource.id,
                    'text': f"{resource.resource_name} ({resource.resource_type})",
                    'resource_name': resource.resource_name,
                    'resource_type': resource.resource_type,
                    'account': resource.aws_account.account_name,
                }
                for resource in resources
            ]
        }

        return JsonResponse(data)
