{% extends 'base.html' %}
{% load static %}

{% block title %}Azure Virtual Machines - Cloud Central SS{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'inventory:dashboard' %}">Home</a></li>
        <li class="breadcrumb-item"><a href="{% url 'azure:dashboard' %}">Azure</a></li>
        <li class="breadcrumb-item active">Virtual Machines</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-desktop me-2"></i>Azure Virtual Machines</h2>
            <div class="btn-group">
                <button class="btn btn-outline-primary" onclick="refreshData()">
                    <i class="fas fa-sync-alt me-1"></i>Refresh
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="get" class="row g-3">
                    <div class="col-md-3">
                        <label for="subscription" class="form-label">Subscription</label>
                        <select name="subscription" id="subscription" class="form-select">
                            <option value="">All Subscriptions</option>
                            {% for subscription in subscriptions %}
                                <option value="{{ subscription.id }}" {% if current_filters.subscription == subscription.id|stringformat:"s" %}selected{% endif %}>
                                    {{ subscription.subscription_name }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="location" class="form-label">Location</label>
                        <select name="location" id="location" class="form-select">
                            <option value="">All Locations</option>
                            {% for location in locations %}
                                <option value="{{ location }}" {% if current_filters.location == location %}selected{% endif %}>
                                    {{ location }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="state" class="form-label">State</label>
                        <select name="state" id="state" class="form-select">
                            <option value="">All States</option>
                            {% for state in states %}
                                <option value="{{ state }}" {% if current_filters.state == state %}selected{% endif %}>
                                    {{ state }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="os_type" class="form-label">OS Type</label>
                        <select name="os_type" id="os_type" class="form-select">
                            <option value="">All OS Types</option>
                            {% for os_type in os_types %}
                                <option value="{{ os_type }}" {% if current_filters.os_type == os_type %}selected{% endif %}>
                                    {{ os_type }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="search" class="form-label">Search</label>
                        <input type="text" name="search" id="search" class="form-control" 
                               placeholder="VM name..." value="{{ current_filters.search }}">
                    </div>
                    <div class="col-md-1">
                        <label class="form-label">&nbsp;</label>
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- VM List -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-desktop me-2"></i>Virtual Machines ({{ vms|length }})</h5>
            </div>
            <div class="card-body">
                {% if vms %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>VM Name</th>
                                    <th>Size</th>
                                    <th>OS</th>
                                    <th>Location</th>
                                    <th>State</th>
                                    <th>Subscription</th>
                                    <th>Last Updated</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for vm in vms %}
                                <tr>
                                    <td>
                                        <div>
                                            <strong>{{ vm.resource_name }}</strong>
                                            {% if vm.computer_name %}
                                                <br><small class="text-muted">{{ vm.computer_name }}</small>
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td>{{ vm.vm_size }}</td>
                                    <td>
                                        <div>
                                            {% if vm.os_type == 'Windows' %}
                                                <i class="fab fa-windows text-primary"></i>
                                            {% elif vm.os_type == 'Linux' %}
                                                <i class="fab fa-linux text-success"></i>
                                            {% endif %}
                                            {{ vm.os_type }}
                                            {% if vm.os_version %}
                                                <br><small class="text-muted">{{ vm.os_version }}</small>
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td>{{ vm.location }}</td>
                                    <td>
                                        {% if vm.state == 'Running' %}
                                            <span class="badge bg-success">{{ vm.state }}</span>
                                        {% elif vm.state == 'Stopped' %}
                                            <span class="badge bg-danger">{{ vm.state }}</span>
                                        {% else %}
                                            <span class="badge bg-warning">{{ vm.state }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small>{{ vm.subscription.subscription_name }}</small>
                                    </td>
                                    <td>
                                        <small>{{ vm.last_updated|timesince }} ago</small>
                                    </td>
                                    <td>
                                        <a href="{% url 'azure:resource_detail' vm.pk %}" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if is_paginated %}
                        <nav aria-label="VM pagination" class="mt-3">
                            <ul class="pagination justify-content-center">
                                {% if page_obj.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page=1">First</a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}">Previous</a>
                                    </li>
                                {% endif %}

                                <li class="page-item active">
                                    <span class="page-link">
                                        Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                                    </span>
                                </li>

                                {% if page_obj.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.next_page_number }}">Next</a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">Last</a>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                    {% endif %}
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-desktop fa-3x text-muted mb-3"></i>
                        <h4 class="text-muted">No Virtual Machines Found</h4>
                        <p class="text-muted">No Azure VMs match your current filters.</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function refreshData() {
    location.reload();
}

// Auto-submit form on filter change
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const selects = form.querySelectorAll('select');
    
    selects.forEach(select => {
        select.addEventListener('change', function() {
            form.submit();
        });
    });
});
</script>
{% endblock %}
