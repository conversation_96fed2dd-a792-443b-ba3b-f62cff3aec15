from django.shortcuts import render, get_object_or_404
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, DetailView
from django.contrib import messages
from django.urls import reverse_lazy
from django.http import JsonResponse
from django.views import View
from .models import PatchCalendar, EKSNodeRefresh, AKSNodeRefresh


class OperationRequiredMixin(UserPassesTestMixin):
    """Mixin to ensure only operation support and admin users can access the view"""
    def test_func(self):
        return self.request.user.is_authenticated and (
            self.request.user.is_superuser or
            (hasattr(self.request.user, 'profile') and
             (self.request.user.profile.is_admin or self.request.user.profile.is_operation_support))
        )


# Patch Calendar Views
class PatchCalendarListView(OperationRequiredMixin, ListView):
    model = PatchCalendar
    template_name = 'compliance/patch_calendar_list.html'
    context_object_name = 'patches'
    paginate_by = 20

    def get_queryset(self):
        return PatchCalendar.objects.select_related('created_by').order_by('scheduled_date')


class PatchCalendarCreateView(OperationRequiredMixin, CreateView):
    model = PatchCalendar
    template_name = 'compliance/patch_calendar_form.html'
    fields = ['title', 'description', 'patch_type', 'scheduled_date',
              'maintenance_window_start', 'maintenance_window_end',
              'aws_accounts', 'resource_types']
    success_url = reverse_lazy('compliance:patch_calendar_list')

    def form_valid(self, form):
        form.instance.created_by = self.request.user
        messages.success(self.request, 'Patch calendar entry created successfully.')
        return super().form_valid(form)


class PatchCalendarDetailView(OperationRequiredMixin, DetailView):
    model = PatchCalendar
    template_name = 'compliance/patch_calendar_detail.html'
    context_object_name = 'patch'


class PatchCalendarEditView(OperationRequiredMixin, UpdateView):
    model = PatchCalendar
    template_name = 'compliance/patch_calendar_form.html'
    fields = ['title', 'description', 'patch_type', 'scheduled_date',
              'maintenance_window_start', 'maintenance_window_end',
              'aws_accounts', 'resource_types', 'status', 'progress_percentage']
    success_url = reverse_lazy('compliance:patch_calendar_list')

    def form_valid(self, form):
        messages.success(self.request, 'Patch calendar entry updated successfully.')
        return super().form_valid(form)


class PatchCalendarDeleteView(OperationRequiredMixin, DeleteView):
    model = PatchCalendar
    template_name = 'compliance/patch_calendar_confirm_delete.html'
    success_url = reverse_lazy('compliance:patch_calendar_list')

    def delete(self, request, *args, **kwargs):
        messages.success(request, 'Patch calendar entry deleted successfully.')
        return super().delete(request, *args, **kwargs)


# EKS Node Refresh Views
class EKSNodeRefreshListView(OperationRequiredMixin, ListView):
    model = EKSNodeRefresh
    template_name = 'compliance/eks_refresh_list.html'
    context_object_name = 'refreshes'
    paginate_by = 20

    def get_queryset(self):
        return EKSNodeRefresh.objects.select_related('aws_account', 'created_by').order_by('-scheduled_date')


class EKSNodeRefreshCreateView(OperationRequiredMixin, CreateView):
    model = EKSNodeRefresh
    template_name = 'compliance/eks_refresh_form.html'
    fields = ['cluster_name', 'aws_account', 'region', 'refresh_type',
              'current_ami', 'target_ami', 'current_k8s_version', 'target_k8s_version',
              'scheduled_date', 'estimated_duration']
    success_url = reverse_lazy('compliance:eks_refresh_list')

    def form_valid(self, form):
        form.instance.created_by = self.request.user
        messages.success(self.request, 'EKS node refresh scheduled successfully.')
        return super().form_valid(form)


class EKSNodeRefreshDetailView(OperationRequiredMixin, DetailView):
    model = EKSNodeRefresh
    template_name = 'compliance/eks_refresh_detail.html'
    context_object_name = 'refresh'


class EKSNodeRefreshEditView(OperationRequiredMixin, UpdateView):
    model = EKSNodeRefresh
    template_name = 'compliance/eks_refresh_form.html'
    fields = ['cluster_name', 'aws_account', 'region', 'refresh_type',
              'current_ami', 'target_ami', 'current_k8s_version', 'target_k8s_version',
              'scheduled_date', 'estimated_duration', 'status', 'progress_percentage']
    success_url = reverse_lazy('compliance:eks_refresh_list')

    def form_valid(self, form):
        messages.success(self.request, 'EKS node refresh updated successfully.')
        return super().form_valid(form)


class EKSNodeRefreshDeleteView(OperationRequiredMixin, DeleteView):
    model = EKSNodeRefresh
    template_name = 'compliance/eks_refresh_confirm_delete.html'
    success_url = reverse_lazy('compliance:eks_refresh_list')

    def delete(self, request, *args, **kwargs):
        messages.success(request, 'EKS node refresh deleted successfully.')
        return super().delete(request, *args, **kwargs)


class EKSNodeRefreshExecuteView(OperationRequiredMixin, View):
    def post(self, request, pk):
        refresh = get_object_or_404(EKSNodeRefresh, pk=pk)
        # TODO: Implement EKS node refresh execution logic
        refresh.status = 'in_progress'
        refresh.save()
        return JsonResponse({'status': 'success', 'message': 'EKS node refresh started'})


# AKS Node Refresh Views
class AKSNodeRefreshListView(OperationRequiredMixin, ListView):
    model = AKSNodeRefresh
    template_name = 'compliance/aks_refresh_list.html'
    context_object_name = 'refreshes'
    paginate_by = 20

    def get_queryset(self):
        return AKSNodeRefresh.objects.select_related('created_by').order_by('-scheduled_date')


class AKSNodeRefreshCreateView(OperationRequiredMixin, CreateView):
    model = AKSNodeRefresh
    template_name = 'compliance/aks_refresh_form.html'
    fields = ['cluster_name', 'subscription_id', 'resource_group', 'refresh_type',
              'current_node_image', 'target_node_image', 'current_k8s_version', 'target_k8s_version',
              'scheduled_date', 'estimated_duration']
    success_url = reverse_lazy('compliance:aks_refresh_list')

    def form_valid(self, form):
        form.instance.created_by = self.request.user
        messages.success(self.request, 'AKS node refresh scheduled successfully.')
        return super().form_valid(form)


class AKSNodeRefreshDetailView(OperationRequiredMixin, DetailView):
    model = AKSNodeRefresh
    template_name = 'compliance/aks_refresh_detail.html'
    context_object_name = 'refresh'


class AKSNodeRefreshEditView(OperationRequiredMixin, UpdateView):
    model = AKSNodeRefresh
    template_name = 'compliance/aks_refresh_form.html'
    fields = ['cluster_name', 'subscription_id', 'resource_group', 'refresh_type',
              'current_node_image', 'target_node_image', 'current_k8s_version', 'target_k8s_version',
              'scheduled_date', 'estimated_duration', 'status', 'progress_percentage']
    success_url = reverse_lazy('compliance:aks_refresh_list')

    def form_valid(self, form):
        messages.success(self.request, 'AKS node refresh updated successfully.')
        return super().form_valid(form)


class AKSNodeRefreshDeleteView(OperationRequiredMixin, DeleteView):
    model = AKSNodeRefresh
    template_name = 'compliance/aks_refresh_confirm_delete.html'
    success_url = reverse_lazy('compliance:aks_refresh_list')

    def delete(self, request, *args, **kwargs):
        messages.success(request, 'AKS node refresh deleted successfully.')
        return super().delete(request, *args, **kwargs)


class AKSNodeRefreshExecuteView(OperationRequiredMixin, View):
    def post(self, request, pk):
        refresh = get_object_or_404(AKSNodeRefresh, pk=pk)
        # TODO: Implement AKS node refresh execution logic
        refresh.status = 'in_progress'
        refresh.save()
        return JsonResponse({'status': 'success', 'message': 'AKS node refresh started'})


# API Views
class PatchCalendarAPIView(OperationRequiredMixin, View):
    def get(self, request):
        patches = PatchCalendar.objects.values(
            'id', 'title', 'patch_type', 'scheduled_date', 'status', 'progress_percentage'
        )
        return JsonResponse(list(patches), safe=False)


class EKSRefreshAPIView(OperationRequiredMixin, View):
    def get(self, request):
        refreshes = EKSNodeRefresh.objects.values(
            'id', 'cluster_name', 'refresh_type', 'scheduled_date', 'status', 'progress_percentage'
        )
        return JsonResponse(list(refreshes), safe=False)


class AKSRefreshAPIView(OperationRequiredMixin, View):
    def get(self, request):
        refreshes = AKSNodeRefresh.objects.values(
            'id', 'cluster_name', 'refresh_type', 'scheduled_date', 'status', 'progress_percentage'
        )
        return JsonResponse(list(refreshes), safe=False)
