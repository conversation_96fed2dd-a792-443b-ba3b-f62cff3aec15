from django.shortcuts import render, get_object_or_404
from django.views.generic import ListView, DetailView, TemplateView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.http import JsonResponse
from django.db.models import Q, Count

from .models import BaseAzureResource, VirtualMachine, StorageAccount, AKSCluster, AzureSubscription


class AzureDashboardView(LoginRequiredMixin, TemplateView):
    template_name = 'azure/dashboard.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get resource counts by type
        resource_counts = {
            'total': BaseAzureResource.objects.count(),
            'vm': VirtualMachine.objects.count(),
            'storage': StorageAccount.objects.count(),
            'aks': AKSCluster.objects.count(),
        }
        
        # Get subscription counts
        context['subscription_count'] = AzureSubscription.objects.filter(is_active=True).count()
        
        # Get recent resources
        context['recent_resources'] = BaseAzureResource.objects.select_related(
            'subscription', 'resource_group'
        ).order_by('-last_updated')[:10]
        
        # Get resource distribution by location
        location_distribution = BaseAzureResource.objects.values('location').annotate(
            count=Count('id')
        ).order_by('-count')[:10]
        
        context.update({
            'resource_counts': resource_counts,
            'location_distribution': location_distribution,
        })
        
        return context


class AzureResourceListView(LoginRequiredMixin, ListView):
    model = BaseAzureResource
    template_name = 'azure/resource_list.html'
    context_object_name = 'resources'
    paginate_by = 50

    def get_queryset(self):
        queryset = BaseAzureResource.objects.select_related(
            'subscription', 'resource_group'
        ).prefetch_related('tags')

        # Apply filters
        resource_type = self.request.GET.get('type')
        if resource_type:
            queryset = queryset.filter(resource_type=resource_type)

        subscription_id = self.request.GET.get('subscription')
        if subscription_id:
            queryset = queryset.filter(subscription_id=subscription_id)

        location = self.request.GET.get('location')
        if location:
            queryset = queryset.filter(location=location)

        state = self.request.GET.get('state')
        if state:
            queryset = queryset.filter(state=state)

        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(resource_name__icontains=search) |
                Q(resource_id__icontains=search)
            )

        return queryset.order_by('-last_updated')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get filter options
        context['resource_types'] = BaseAzureResource.RESOURCE_TYPES
        context['subscriptions'] = AzureSubscription.objects.filter(is_active=True)
        context['locations'] = BaseAzureResource.objects.values_list('location', flat=True).distinct()
        context['states'] = BaseAzureResource.objects.values_list('state', flat=True).distinct()
        
        # Current filters
        context['current_filters'] = {
            'type': self.request.GET.get('type', ''),
            'subscription': self.request.GET.get('subscription', ''),
            'location': self.request.GET.get('location', ''),
            'state': self.request.GET.get('state', ''),
            'search': self.request.GET.get('search', ''),
        }
        
        return context


class AzureResourceTypeListView(AzureResourceListView):
    def get_queryset(self):
        queryset = super().get_queryset()
        resource_type = self.kwargs.get('resource_type', '').upper()
        return queryset.filter(resource_type=resource_type)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['resource_type'] = self.kwargs.get('resource_type', '').upper()
        return context


class AzureResourceDetailView(LoginRequiredMixin, DetailView):
    model = BaseAzureResource
    template_name = 'azure/resource_detail.html'
    context_object_name = 'resource'

    def get_queryset(self):
        return BaseAzureResource.objects.select_related(
            'subscription', 'resource_group'
        ).prefetch_related('tags')


class VirtualMachineListView(LoginRequiredMixin, ListView):
    model = VirtualMachine
    template_name = 'azure/vm_list.html'
    context_object_name = 'vms'
    paginate_by = 50

    def get_queryset(self):
        queryset = VirtualMachine.objects.select_related(
            'subscription', 'resource_group'
        ).prefetch_related('tags')

        # Apply filters
        subscription_id = self.request.GET.get('subscription')
        if subscription_id:
            queryset = queryset.filter(subscription_id=subscription_id)

        location = self.request.GET.get('location')
        if location:
            queryset = queryset.filter(location=location)

        state = self.request.GET.get('state')
        if state:
            queryset = queryset.filter(state=state)

        os_type = self.request.GET.get('os_type')
        if os_type:
            queryset = queryset.filter(os_type=os_type)

        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(resource_name__icontains=search) |
                Q(computer_name__icontains=search)
            )

        return queryset.order_by('-last_updated')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get filter options
        context['subscriptions'] = AzureSubscription.objects.filter(is_active=True)
        context['locations'] = VirtualMachine.objects.values_list('location', flat=True).distinct()
        context['states'] = VirtualMachine.objects.values_list('state', flat=True).distinct()
        context['os_types'] = VirtualMachine.objects.values_list('os_type', flat=True).distinct()
        
        # Current filters
        context['current_filters'] = {
            'subscription': self.request.GET.get('subscription', ''),
            'location': self.request.GET.get('location', ''),
            'state': self.request.GET.get('state', ''),
            'os_type': self.request.GET.get('os_type', ''),
            'search': self.request.GET.get('search', ''),
        }
        
        return context


class StorageAccountListView(LoginRequiredMixin, ListView):
    model = StorageAccount
    template_name = 'azure/storage_list.html'
    context_object_name = 'storage_accounts'
    paginate_by = 50

    def get_queryset(self):
        queryset = StorageAccount.objects.select_related(
            'subscription', 'resource_group'
        ).prefetch_related('tags')

        # Apply filters
        subscription_id = self.request.GET.get('subscription')
        if subscription_id:
            queryset = queryset.filter(subscription_id=subscription_id)

        location = self.request.GET.get('location')
        if location:
            queryset = queryset.filter(location=location)

        sku_name = self.request.GET.get('sku_name')
        if sku_name:
            queryset = queryset.filter(sku_name=sku_name)

        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(resource_name__icontains=search)

        return queryset.order_by('-last_updated')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get filter options
        context['subscriptions'] = AzureSubscription.objects.filter(is_active=True)
        context['locations'] = StorageAccount.objects.values_list('location', flat=True).distinct()
        context['sku_names'] = StorageAccount.objects.values_list('sku_name', flat=True).distinct()
        
        # Current filters
        context['current_filters'] = {
            'subscription': self.request.GET.get('subscription', ''),
            'location': self.request.GET.get('location', ''),
            'sku_name': self.request.GET.get('sku_name', ''),
            'search': self.request.GET.get('search', ''),
        }
        
        return context


# API Views
class AzureResourceListAPIView(LoginRequiredMixin, TemplateView):
    def get(self, request, *args, **kwargs):
        queryset = BaseAzureResource.objects.select_related(
            'subscription', 'resource_group'
        ).prefetch_related('tags')

        # Apply filters
        resource_type = request.GET.get('type')
        if resource_type:
            queryset = queryset.filter(resource_type=resource_type)

        subscription_id = request.GET.get('subscription')
        if subscription_id:
            queryset = queryset.filter(subscription_id=subscription_id)

        location = request.GET.get('location')
        if location:
            queryset = queryset.filter(location=location)

        search = request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(resource_name__icontains=search) |
                Q(resource_id__icontains=search)
            )

        queryset = queryset.order_by('-last_updated')

        # Pagination
        from django.core.paginator import Paginator
        page = request.GET.get('page', 1)
        paginator = Paginator(queryset, 50)
        resources = paginator.get_page(page)

        data = {
            'count': paginator.count,
            'num_pages': paginator.num_pages,
            'current_page': resources.number,
            'results': [
                {
                    'id': resource.id,
                    'resource_name': resource.resource_name,
                    'resource_type': resource.resource_type,
                    'subscription': resource.subscription.subscription_name,
                    'location': resource.location,
                    'state': resource.state,
                    'last_updated': resource.last_updated.isoformat(),
                }
                for resource in resources
            ]
        }

        return JsonResponse(data)
