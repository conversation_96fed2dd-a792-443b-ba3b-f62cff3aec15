<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Cloud Central SS - AWS Resource Inventory{% endblock %}</title>

    <!-- Favicon -->
    {% load static %}
    <link rel="icon" type="image/png" href="{% static 'images/GEV-e06a174f.png' %}">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="{% static 'css/custom.css' %}" rel="stylesheet">

    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-vernova">
        <div class="container-fluid">
            <!-- Brand -->
            <a class="navbar-brand d-flex align-items-center" href="{% url 'inventory:dashboard' %}">
                <img src="{% static 'images/ge_vernova.svg' %}" alt="GE Vernova" height="40" class="me-2">
                <span class="fw-bold">Cloud Central SS</span>
            </a>

            <!-- Toggle button for mobile -->
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <!-- Navigation items -->
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    {% if user.is_authenticated %}
                        <li class="nav-item">
                            <a class="nav-link {% if request.resolver_match.app_name == 'inventory' %}active{% endif %}"
                               href="{% url 'inventory:dashboard' %}">
                                <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.resolver_match.app_name == 'inventory' and 'resource' in request.resolver_match.url_name %}active{% endif %}"
                               href="{% url 'inventory:resource_list' %}">
                                <i class="fas fa-server me-1"></i>Resources
                            </a>
                        </li>

                        {% if user.profile.is_admin or user.profile.is_operation_support %}
                        <li class="nav-item">
                            <a class="nav-link {% if request.resolver_match.app_name == 'jobs' %}active{% endif %}"
                               href="{% url 'jobs:dashboard' %}">
                                <i class="fas fa-tasks me-1"></i>Jobs
                            </a>
                        </li>
                        {% endif %}

                        {% if user.profile.is_admin %}
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-cog me-1"></i>Administration
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{% url 'aws_session:session_list' %}">
                                    <i class="fas fa-key me-1"></i>AWS Sessions
                                </a></li>
                                <li><a class="dropdown-item" href="{% url 'authentication:user_list' %}">
                                    <i class="fas fa-users me-1"></i>Users
                                </a></li>
                                <li><a class="dropdown-item" href="{% url 'authentication:group_list' %}">
                                    <i class="fas fa-users-cog me-1"></i>Groups
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{% url 'admin:index' %}">
                                    <i class="fas fa-tools me-1"></i>Django Admin
                                </a></li>
                            </ul>
                        </li>
                        {% endif %}
                    {% endif %}
                </ul>

                <!-- User menu -->
                <ul class="navbar-nav">
                    {% if user.is_authenticated %}
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-1"></i>{{ user.get_full_name|default:user.username }}
                                <span class="badge bg-secondary ms-1">{{ user.profile.user_group.name|default:"No Group" }}</span>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><a class="dropdown-item" href="{% url 'authentication:profile' %}">
                                    <i class="fas fa-user-edit me-1"></i>Profile
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{% url 'authentication:logout' %}">
                                    <i class="fas fa-sign-out-alt me-1"></i>Logout
                                </a></li>
                            </ul>
                        </li>
                    {% else %}
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'authentication:login' %}">
                                <i class="fas fa-sign-in-alt me-1"></i>Login
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main content -->
    <main class="container-fluid py-4">
        <!-- Messages -->
        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endfor %}
        {% endif %}

        <!-- Breadcrumb -->
        {% block breadcrumb %}{% endblock %}

        <!-- Page content -->
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-light py-3 mt-5">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-6">
                    <p class="mb-0 text-muted">
                        <small>&copy; 2024 GE Vernova. Cloud Central SS - AWS Resource Inventory System</small>
                    </p>
                </div>
                <div class="col-md-6 text-end">
                    <p class="mb-0 text-muted">
                        <small>Version 1.0.0</small>
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Custom JS -->
    <script src="{% static 'js/custom.js' %}"></script>

    {% block extra_js %}{% endblock %}
</body>
</html>
