{% extends 'base.html' %}
{% load static %}

{% block title %}Azure Dashboard - Cloud Central SS{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'inventory:dashboard' %}">Home</a></li>
        <li class="breadcrumb-item active">Azure Dashboard</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fab fa-microsoft me-2"></i>Azure Resources Dashboard</h2>
            <div class="btn-group">
                <a href="{% url 'azure:resource_list' %}" class="btn btn-primary">
                    <i class="fas fa-list me-1"></i>View All Resources
                </a>
                <button class="btn btn-outline-primary" onclick="refreshData()">
                    <i class="fas fa-sync-alt me-1"></i>Refresh
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Resource Overview Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ resource_counts.total }}</h4>
                        <p class="card-text">Total Resources</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-server fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ resource_counts.vm }}</h4>
                        <p class="card-text">Virtual Machines</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-desktop fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ resource_counts.storage }}</h4>
                        <p class="card-text">Storage Accounts</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-hdd fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ subscription_count }}</h4>
                        <p class="card-text">Subscriptions</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-credit-card fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Resource Distribution -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-map-marker-alt me-2"></i>Resources by Location</h5>
            </div>
            <div class="card-body">
                {% if location_distribution %}
                    {% for location in location_distribution %}
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>{{ location.location }}</span>
                            <span class="badge bg-primary">{{ location.count }}</span>
                        </div>
                    {% endfor %}
                {% else %}
                    <p class="text-muted">No location data available</p>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-clock me-2"></i>Recent Resources</h5>
            </div>
            <div class="card-body">
                {% if recent_resources %}
                    {% for resource in recent_resources %}
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <div>
                                <strong>{{ resource.resource_name }}</strong>
                                <br>
                                <small class="text-muted">{{ resource.resource_type }} • {{ resource.location }}</small>
                            </div>
                            <small class="text-muted">{{ resource.last_updated|timesince }} ago</small>
                        </div>
                    {% endfor %}
                {% else %}
                    <p class="text-muted">No recent resources</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-bolt me-2"></i>Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <a href="{% url 'azure:vm_list' %}" class="btn btn-outline-primary w-100 mb-2">
                            <i class="fas fa-desktop me-2"></i>View Virtual Machines
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="{% url 'azure:storage_list' %}" class="btn btn-outline-primary w-100 mb-2">
                            <i class="fas fa-hdd me-2"></i>View Storage Accounts
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="#" class="btn btn-outline-secondary w-100 mb-2" disabled>
                            <i class="fas fa-dharmachakra me-2"></i>AKS Clusters
                            <span class="badge bg-warning ms-1">Soon</span>
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="{% url 'azure:resource_list' %}" class="btn btn-outline-primary w-100 mb-2">
                            <i class="fas fa-search me-2"></i>Search All Resources
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function refreshData() {
    location.reload();
}

// Auto-refresh every 5 minutes
setInterval(refreshData, 300000);
</script>
{% endblock %}
