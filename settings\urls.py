from django.urls import path
from . import views

app_name = 'settings'

urlpatterns = [
    # Azure Session Management
    path('azure-session/', views.AzureSessionListView.as_view(), name='azure_session_list'),
    path('azure-session/create/', views.AzureSessionCreateView.as_view(), name='azure_session_create'),
    path('azure-session/<uuid:pk>/', views.AzureSessionDetailView.as_view(), name='azure_session_detail'),
    path('azure-session/<uuid:pk>/edit/', views.AzureSessionEditView.as_view(), name='azure_session_edit'),
    path('azure-session/<uuid:pk>/delete/', views.AzureSessionDeleteView.as_view(), name='azure_session_delete'),
    path('azure-session/<uuid:pk>/test/', views.AzureSessionTestView.as_view(), name='azure_session_test'),
    
    # User Management
    path('user-management/', views.UserManagementView.as_view(), name='user_management'),
    path('user-management/create/', views.UserCreateView.as_view(), name='user_create'),
    path('user-management/<int:pk>/edit/', views.UserEditView.as_view(), name='user_edit'),
    path('user-management/<int:pk>/delete/', views.UserDeleteView.as_view(), name='user_delete'),
    path('user-management/<int:pk>/groups/', views.UserGroupManagementView.as_view(), name='user_group_management'),
    
    # App Configuration
    path('app-configuration/', views.AppConfigurationView.as_view(), name='app_configuration'),
    path('app-configuration/create/', views.ConfigCreateView.as_view(), name='config_create'),
    path('app-configuration/<int:pk>/edit/', views.ConfigEditView.as_view(), name='config_edit'),
    path('app-configuration/<int:pk>/delete/', views.ConfigDeleteView.as_view(), name='config_delete'),
    
    # API endpoints
    path('api/azure-sessions/', views.AzureSessionListAPIView.as_view(), name='api_azure_session_list'),
    path('api/users/', views.UserListAPIView.as_view(), name='api_user_list'),
    path('api/config/', views.ConfigListAPIView.as_view(), name='api_config_list'),
]
