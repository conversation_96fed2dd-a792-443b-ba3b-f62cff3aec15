from django.urls import path
from . import views

app_name = 'operations'

urlpatterns = [
    # Dashboard
    path('', views.OperationsDashboardView.as_view(), name='dashboard'),
    
    # Automation Templates
    path('templates/', views.AutomationTemplateListView.as_view(), name='template_list'),
    path('templates/<uuid:pk>/', views.AutomationTemplateDetailView.as_view(), name='template_detail'),
    path('templates/<uuid:template_id>/run/', views.RunTemplateView.as_view(), name='run_template'),
    
    # Ad-Hoc Runs
    path('adhoc/', views.AdHocRunListView.as_view(), name='adhoc_list'),
    path('adhoc/create/', views.AdHocRunCreateView.as_view(), name='adhoc_create'),
    path('adhoc/<uuid:pk>/', views.AdHocRunDetailView.as_view(), name='adhoc_detail'),
    
    # API endpoints
    path('api/templates/', views.TemplateListAPIView.as_view(), name='api_template_list'),
    path('api/runs/<uuid:run_id>/status/', views.RunStatusAPIView.as_view(), name='api_run_status'),
]
