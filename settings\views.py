from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin
from django.contrib.auth.models import User, Group
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, DetailView, TemplateView
from django.contrib import messages
from django.urls import reverse_lazy
from django.http import JsonResponse
from django.views import View
from .models import AzureSession, AppConfiguration, UserManagement


class AdminRequiredMixin(UserPassesTestMixin):
    """Mixin to ensure only admin users can access the view"""
    def test_func(self):
        return self.request.user.is_authenticated and (
            self.request.user.is_superuser or
            hasattr(self.request.user, 'profile') and self.request.user.profile.is_admin
        )


# Azure Session Management Views
class AzureSessionListView(AdminRequiredMixin, ListView):
    model = AzureSession
    template_name = 'settings/azure_session_list.html'
    context_object_name = 'sessions'
    paginate_by = 20

    def get_queryset(self):
        return AzureSession.objects.filter(is_active=True).order_by('-created_at')


class AzureSessionCreateView(AdminRequiredMixin, CreateView):
    model = AzureSession
    template_name = 'settings/azure_session_form.html'
    fields = ['name', 'description', 'auth_method', 'tenant_id', 'client_id',
              'client_secret', 'subscription_id', 'subscription_name',
              'default_resource_group', 'default_location']
    success_url = reverse_lazy('settings:azure_session_list')

    def form_valid(self, form):
        form.instance.created_by = self.request.user
        messages.success(self.request, 'Azure session created successfully.')
        return super().form_valid(form)


class AzureSessionDetailView(AdminRequiredMixin, DetailView):
    model = AzureSession
    template_name = 'settings/azure_session_detail.html'
    context_object_name = 'session'


class AzureSessionEditView(AdminRequiredMixin, UpdateView):
    model = AzureSession
    template_name = 'settings/azure_session_form.html'
    fields = ['name', 'description', 'auth_method', 'tenant_id', 'client_id',
              'client_secret', 'subscription_id', 'subscription_name',
              'default_resource_group', 'default_location', 'is_active']
    success_url = reverse_lazy('settings:azure_session_list')

    def form_valid(self, form):
        messages.success(self.request, 'Azure session updated successfully.')
        return super().form_valid(form)


class AzureSessionDeleteView(AdminRequiredMixin, DeleteView):
    model = AzureSession
    template_name = 'settings/azure_session_confirm_delete.html'
    success_url = reverse_lazy('settings:azure_session_list')

    def delete(self, request, *args, **kwargs):
        messages.success(request, 'Azure session deleted successfully.')
        return super().delete(request, *args, **kwargs)


class AzureSessionTestView(AdminRequiredMixin, View):
    def post(self, request, pk):
        session = get_object_or_404(AzureSession, pk=pk)
        # TODO: Implement Azure connection test
        return JsonResponse({'status': 'success', 'message': 'Connection test successful'})


# User Management Views
class UserManagementView(AdminRequiredMixin, ListView):
    model = User
    template_name = 'settings/user_management.html'
    context_object_name = 'users'
    paginate_by = 20

    def get_queryset(self):
        return User.objects.select_related('profile').order_by('username')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['groups'] = Group.objects.all()
        context['recent_actions'] = UserManagement.objects.select_related(
            'target_user', 'performed_by'
        ).order_by('-timestamp')[:10]
        return context


class UserCreateView(AdminRequiredMixin, TemplateView):
    template_name = 'settings/user_form.html'

    def post(self, request):
        # TODO: Implement user creation logic
        messages.success(request, 'User created successfully.')
        return redirect('settings:user_management')


class UserEditView(AdminRequiredMixin, TemplateView):
    template_name = 'settings/user_form.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['user_obj'] = get_object_or_404(User, pk=kwargs['pk'])
        return context


class UserDeleteView(AdminRequiredMixin, TemplateView):
    template_name = 'settings/user_confirm_delete.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['user_obj'] = get_object_or_404(User, pk=kwargs['pk'])
        return context


class UserGroupManagementView(AdminRequiredMixin, TemplateView):
    template_name = 'settings/user_groups.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['user_obj'] = get_object_or_404(User, pk=kwargs['pk'])
        context['groups'] = Group.objects.all()
        return context


# App Configuration Views
class AppConfigurationView(AdminRequiredMixin, ListView):
    model = AppConfiguration
    template_name = 'settings/app_configuration.html'
    context_object_name = 'configs'
    paginate_by = 50

    def get_queryset(self):
        return AppConfiguration.objects.order_by('category', 'key')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['categories'] = AppConfiguration.objects.values_list(
            'category', flat=True
        ).distinct()
        return context


class ConfigCreateView(AdminRequiredMixin, CreateView):
    model = AppConfiguration
    template_name = 'settings/config_form.html'
    fields = ['key', 'value', 'config_type', 'description', 'category', 'is_sensitive']
    success_url = reverse_lazy('settings:app_configuration')

    def form_valid(self, form):
        form.instance.created_by = self.request.user
        messages.success(self.request, 'Configuration created successfully.')
        return super().form_valid(form)


class ConfigEditView(AdminRequiredMixin, UpdateView):
    model = AppConfiguration
    template_name = 'settings/config_form.html'
    fields = ['key', 'value', 'config_type', 'description', 'category', 'is_sensitive']
    success_url = reverse_lazy('settings:app_configuration')

    def form_valid(self, form):
        messages.success(self.request, 'Configuration updated successfully.')
        return super().form_valid(form)


class ConfigDeleteView(AdminRequiredMixin, DeleteView):
    model = AppConfiguration
    template_name = 'settings/config_confirm_delete.html'
    success_url = reverse_lazy('settings:app_configuration')

    def delete(self, request, *args, **kwargs):
        messages.success(request, 'Configuration deleted successfully.')
        return super().delete(request, *args, **kwargs)


# API Views
class AzureSessionListAPIView(AdminRequiredMixin, View):
    def get(self, request):
        sessions = AzureSession.objects.filter(is_active=True).values(
            'id', 'name', 'auth_method', 'subscription_name', 'test_status'
        )
        return JsonResponse(list(sessions), safe=False)


class UserListAPIView(AdminRequiredMixin, View):
    def get(self, request):
        users = User.objects.values(
            'id', 'username', 'email', 'first_name', 'last_name', 'is_active'
        )
        return JsonResponse(list(users), safe=False)


class ConfigListAPIView(AdminRequiredMixin, View):
    def get(self, request):
        configs = AppConfiguration.objects.values(
            'id', 'key', 'value', 'config_type', 'category'
        )
        return JsonResponse(list(configs), safe=False)
