{% extends 'base.html' %}
{% load static %}

{% block title %}Automation Templates - Cloud Central SS{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'inventory:dashboard' %}">Home</a></li>
        <li class="breadcrumb-item"><a href="{% url 'operations:dashboard' %}">Operations</a></li>
        <li class="breadcrumb-item active">Automation Templates</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-file-code me-2"></i>Automation Templates</h2>
            <div class="btn-group">
                <a href="{% url 'operations:adhoc_create' %}" class="btn btn-success">
                    <i class="fas fa-play me-1"></i>Create Ad-Hoc Run
                </a>
                <button class="btn btn-outline-primary" onclick="refreshData()">
                    <i class="fas fa-sync-alt me-1"></i>Refresh
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="get" class="row g-3">
                    <div class="col-md-3">
                        <label for="type" class="form-label">Template Type</label>
                        <select name="type" id="type" class="form-select">
                            <option value="">All Types</option>
                            {% for type_code, type_name in template_types %}
                                <option value="{{ type_code }}" {% if current_filters.type == type_code %}selected{% endif %}>
                                    {{ type_name }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="provider" class="form-label">Cloud Provider</label>
                        <select name="provider" id="provider" class="form-select">
                            <option value="">All Providers</option>
                            {% for provider_code, provider_name in cloud_providers %}
                                <option value="{{ provider_code }}" {% if current_filters.provider == provider_code %}selected{% endif %}>
                                    {{ provider_name }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="search" class="form-label">Search</label>
                        <input type="text" name="search" id="search" class="form-control" 
                               placeholder="Template name or description..." value="{{ current_filters.search }}">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Templates Grid -->
<div class="row">
    {% if templates %}
        {% for template in templates %}
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">{{ template.name }}</h6>
                    <div>
                        {% if template.cloud_provider == 'aws' %}
                            <span class="badge bg-warning"><i class="fab fa-aws"></i> AWS</span>
                        {% elif template.cloud_provider == 'azure' %}
                            <span class="badge bg-info"><i class="fab fa-microsoft"></i> Azure</span>
                        {% elif template.cloud_provider == 'multi' %}
                            <span class="badge bg-success"><i class="fas fa-cloud"></i> Multi-Cloud</span>
                        {% endif %}
                    </div>
                </div>
                <div class="card-body">
                    <p class="card-text">{{ template.description|truncatechars:100 }}</p>
                    
                    <div class="mb-3">
                        <span class="badge bg-secondary">{{ template.get_template_type_display }}</span>
                        {% if template.is_public %}
                            <span class="badge bg-success">Public</span>
                        {% else %}
                            <span class="badge bg-warning">Private</span>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <small class="text-muted">
                            <i class="fas fa-user me-1"></i>{{ template.created_by.username }}
                            <span class="ms-2">
                                <i class="fas fa-calendar me-1"></i>{{ template.created_at|date:"M d, Y" }}
                            </span>
                        </small>
                    </div>
                    
                    <div class="mb-3">
                        <small class="text-muted">
                            <i class="fas fa-play-circle me-1"></i>Used {{ template.usage_count }} times
                            {% if template.last_used %}
                                <span class="ms-2">
                                    <i class="fas fa-clock me-1"></i>Last: {{ template.last_used|timesince }} ago
                                </span>
                            {% endif %}
                        </small>
                    </div>
                </div>
                <div class="card-footer">
                    <div class="btn-group w-100">
                        <a href="{% url 'operations:template_detail' template.pk %}" class="btn btn-outline-primary">
                            <i class="fas fa-eye"></i> View
                        </a>
                        <form method="post" action="{% url 'operations:run_template' template.pk %}" class="d-inline">
                            {% csrf_token %}
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-play"></i> Run
                            </button>
                        </form>
                        <a href="{% url 'operations:adhoc_create' %}?template={{ template.pk }}" class="btn btn-outline-success">
                            <i class="fas fa-cog"></i> Configure
                        </a>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    {% else %}
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-file-code fa-3x text-muted mb-3"></i>
                <h4 class="text-muted">No Templates Found</h4>
                <p class="text-muted">No automation templates match your current filters.</p>
                <a href="{% url 'operations:adhoc_create' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>Create Custom Run
                </a>
            </div>
        </div>
    {% endif %}
</div>

<!-- Pagination -->
{% if is_paginated %}
    <nav aria-label="Templates pagination" class="mt-4">
        <ul class="pagination justify-content-center">
            {% if page_obj.has_previous %}
                <li class="page-item">
                    <a class="page-link" href="?page=1">First</a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}">Previous</a>
                </li>
            {% endif %}

            <li class="page-item active">
                <span class="page-link">
                    Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                </span>
            </li>

            {% if page_obj.has_next %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.next_page_number }}">Next</a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">Last</a>
                </li>
            {% endif %}
        </ul>
    </nav>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
function refreshData() {
    location.reload();
}

// Auto-submit form on filter change
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const selects = form.querySelectorAll('select');
    
    selects.forEach(select => {
        select.addEventListener('change', function() {
            form.submit();
        });
    });
});
</script>
{% endblock %}
