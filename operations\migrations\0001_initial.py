# Generated by Django 4.2.7 on 2025-05-29 16:25

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AutomationTemplate',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField()),
                ('template_type', models.CharField(choices=[('resource_cleanup', 'Resource Cleanup'), ('security_audit', 'Security Audit'), ('cost_optimization', 'Cost Optimization'), ('compliance_check', 'Compliance Check'), ('backup_verification', 'Backup Verification'), ('performance_analysis', 'Performance Analysis'), ('custom_script', 'Custom Script')], max_length=30)),
                ('cloud_provider', models.CharField(choices=[('aws', 'Amazon Web Services'), ('azure', 'Microsoft Azure'), ('multi', 'Multi-Cloud')], max_length=10)),
                ('parameters', models.JSONField(default=dict, help_text='Template parameters schema')),
                ('script_content', models.TextField(blank=True, help_text='Script or automation content')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('is_public', models.BooleanField(default=False, help_text='Available to all users')),
                ('usage_count', models.IntegerField(default=0)),
                ('last_used', models.DateTimeField(blank=True, null=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='automation_templates', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Automation Template',
                'verbose_name_plural': 'Automation Templates',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='AdHocRun',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True)),
                ('custom_script', models.TextField(blank=True, help_text='Custom script if not using template')),
                ('parameters', models.JSONField(default=dict, help_text='Runtime parameters')),
                ('target_resources', models.JSONField(default=list, help_text='Target resource IDs')),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('running', 'Running'), ('completed', 'Completed'), ('failed', 'Failed'), ('cancelled', 'Cancelled')], default='pending', max_length=20)),
                ('started_at', models.DateTimeField(auto_now_add=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('output', models.TextField(blank=True)),
                ('error_message', models.TextField(blank=True)),
                ('exit_code', models.IntegerField(blank=True, null=True)),
                ('execution_time', models.FloatField(blank=True, help_text='Execution time in seconds', null=True)),
                ('resources_affected', models.IntegerField(default=0)),
                ('started_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='adhoc_runs', to=settings.AUTH_USER_MODEL)),
                ('template', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='operations.automationtemplate')),
            ],
            options={
                'verbose_name': 'Ad-Hoc Run',
                'verbose_name_plural': 'Ad-Hoc Runs',
                'ordering': ['-started_at'],
            },
        ),
        migrations.CreateModel(
            name='RunStep',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('step_number', models.IntegerField()),
                ('step_name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('running', 'Running'), ('completed', 'Completed'), ('failed', 'Failed'), ('skipped', 'Skipped')], default='pending', max_length=20)),
                ('started_at', models.DateTimeField(blank=True, null=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('output', models.TextField(blank=True)),
                ('error_message', models.TextField(blank=True)),
                ('run', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='steps', to='operations.adhocrun')),
            ],
            options={
                'verbose_name': 'Run Step',
                'verbose_name_plural': 'Run Steps',
                'ordering': ['step_number'],
                'unique_together': {('run', 'step_number')},
            },
        ),
    ]
