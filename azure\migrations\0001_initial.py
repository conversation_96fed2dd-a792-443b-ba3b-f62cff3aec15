# Generated by Django 4.2.7 on 2025-05-29 16:25

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('inventory', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='AzureResourceGroup',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=90)),
                ('location', models.CharField(max_length=50)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': 'Azure Resource Group',
                'verbose_name_plural': 'Azure Resource Groups',
            },
        ),
        migrations.CreateModel(
            name='AzureSubscription',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('subscription_id', models.CharField(max_length=36, unique=True)),
                ('subscription_name', models.CharField(max_length=100)),
                ('tenant_id', models.CharField(max_length=36)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('business_unit', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='azure_subscriptions', to='inventory.businessunit')),
            ],
            options={
                'verbose_name': 'Azure Subscription',
                'verbose_name_plural': 'Azure Subscriptions',
            },
        ),
        migrations.CreateModel(
            name='BaseAzureResource',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('resource_id', models.CharField(max_length=500)),
                ('resource_type', models.CharField(choices=[('VM', 'Virtual Machine'), ('STORAGE', 'Storage Account'), ('AKS', 'AKS Cluster'), ('WEBAPP', 'Web App'), ('SQLDB', 'SQL Database'), ('COSMOSDB', 'Cosmos DB')], max_length=20)),
                ('resource_name', models.CharField(max_length=255)),
                ('location', models.CharField(max_length=50)),
                ('state', models.CharField(blank=True, max_length=50)),
                ('created_date', models.DateTimeField(blank=True, null=True)),
                ('last_updated', models.DateTimeField(auto_now=True)),
                ('data_collected_at', models.DateTimeField(auto_now_add=True)),
                ('resource_group', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='resources', to='azure.azureresourcegroup')),
                ('subscription', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='resources', to='azure.azuresubscription')),
            ],
            options={
                'verbose_name': 'Base Azure Resource',
                'verbose_name_plural': 'Base Azure Resources',
            },
        ),
        migrations.CreateModel(
            name='AKSCluster',
            fields=[
                ('baseazureresource_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='azure.baseazureresource')),
                ('kubernetes_version', models.CharField(blank=True, max_length=20)),
                ('dns_prefix', models.CharField(blank=True, max_length=54)),
                ('fqdn', models.CharField(blank=True, max_length=255)),
                ('node_count', models.IntegerField(blank=True, null=True)),
                ('vm_size', models.CharField(blank=True, max_length=50)),
                ('network_plugin', models.CharField(blank=True, max_length=20)),
            ],
            options={
                'verbose_name': 'Azure AKS Cluster',
                'verbose_name_plural': 'Azure AKS Clusters',
            },
            bases=('azure.baseazureresource',),
        ),
        migrations.CreateModel(
            name='StorageAccount',
            fields=[
                ('baseazureresource_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='azure.baseazureresource')),
                ('account_type', models.CharField(blank=True, max_length=50)),
                ('sku_name', models.CharField(blank=True, max_length=50)),
                ('sku_tier', models.CharField(blank=True, max_length=20)),
                ('access_tier', models.CharField(blank=True, max_length=20)),
                ('https_traffic_only', models.BooleanField(default=True)),
                ('allow_blob_public_access', models.BooleanField(default=False)),
            ],
            options={
                'verbose_name': 'Azure Storage Account',
                'verbose_name_plural': 'Azure Storage Accounts',
            },
            bases=('azure.baseazureresource',),
        ),
        migrations.CreateModel(
            name='VirtualMachine',
            fields=[
                ('baseazureresource_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='azure.baseazureresource')),
                ('vm_size', models.CharField(max_length=50)),
                ('os_type', models.CharField(blank=True, max_length=20)),
                ('os_version', models.CharField(blank=True, max_length=100)),
                ('computer_name', models.CharField(blank=True, max_length=64)),
                ('admin_username', models.CharField(blank=True, max_length=64)),
                ('public_ip', models.GenericIPAddressField(blank=True, null=True)),
                ('private_ip', models.GenericIPAddressField(blank=True, null=True)),
                ('power_state', models.CharField(blank=True, max_length=20)),
            ],
            options={
                'verbose_name': 'Azure Virtual Machine',
                'verbose_name_plural': 'Azure Virtual Machines',
            },
            bases=('azure.baseazureresource',),
        ),
        migrations.CreateModel(
            name='AzureTag',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('key', models.CharField(max_length=255)),
                ('value', models.CharField(blank=True, max_length=255)),
                ('resource', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tags', to='azure.baseazureresource')),
            ],
            options={
                'verbose_name': 'Azure Tag',
                'verbose_name_plural': 'Azure Tags',
            },
        ),
        migrations.AddField(
            model_name='azureresourcegroup',
            name='subscription',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='resource_groups', to='azure.azuresubscription'),
        ),
        migrations.AddIndex(
            model_name='baseazureresource',
            index=models.Index(fields=['resource_type', 'subscription'], name='azure_basea_resourc_a783f2_idx'),
        ),
        migrations.AddIndex(
            model_name='baseazureresource',
            index=models.Index(fields=['resource_name'], name='azure_basea_resourc_16564b_idx'),
        ),
        migrations.AddIndex(
            model_name='baseazureresource',
            index=models.Index(fields=['state'], name='azure_basea_state_ec8873_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='baseazureresource',
            unique_together={('resource_id', 'subscription', 'resource_type')},
        ),
        migrations.AddIndex(
            model_name='azuretag',
            index=models.Index(fields=['key', 'value'], name='azure_azure_key_2d7093_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='azuretag',
            unique_together={('resource', 'key')},
        ),
        migrations.AlterUniqueTogether(
            name='azureresourcegroup',
            unique_together={('name', 'subscription')},
        ),
    ]
