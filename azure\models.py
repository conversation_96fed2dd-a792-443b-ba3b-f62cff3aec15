from django.db import models
from django.contrib.auth.models import User
import uuid


class AzureSubscription(models.Model):
    """Azure Subscription model"""
    subscription_id = models.CharField(max_length=36, unique=True)
    subscription_name = models.CharField(max_length=100)
    tenant_id = models.CharField(max_length=36)
    business_unit = models.ForeignKey('inventory.BusinessUnit', on_delete=models.CASCADE, related_name='azure_subscriptions')
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Azure Subscription"
        verbose_name_plural = "Azure Subscriptions"

    def __str__(self):
        return f"{self.subscription_name} ({self.subscription_id})"


class AzureResourceGroup(models.Model):
    """Azure Resource Group model"""
    name = models.CharField(max_length=90)
    subscription = models.ForeignKey(AzureSubscription, on_delete=models.CASCADE, related_name='resource_groups')
    location = models.Char<PERSON>ield(max_length=50)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "Azure Resource Group"
        verbose_name_plural = "Azure Resource Groups"
        unique_together = ['name', 'subscription']

    def __str__(self):
        return f"{self.name} ({self.subscription.subscription_name})"


class BaseAzureResource(models.Model):
    """Base model for all Azure resources"""
    RESOURCE_TYPES = [
        ('VM', 'Virtual Machine'),
        ('STORAGE', 'Storage Account'),
        ('AKS', 'AKS Cluster'),
        ('WEBAPP', 'Web App'),
        ('SQLDB', 'SQL Database'),
        ('COSMOSDB', 'Cosmos DB'),
    ]

    resource_id = models.CharField(max_length=500)
    resource_type = models.CharField(max_length=20, choices=RESOURCE_TYPES)
    resource_name = models.CharField(max_length=255)
    subscription = models.ForeignKey(AzureSubscription, on_delete=models.CASCADE, related_name='resources')
    resource_group = models.ForeignKey(AzureResourceGroup, on_delete=models.CASCADE, related_name='resources')
    location = models.CharField(max_length=50)
    state = models.CharField(max_length=50, blank=True)
    created_date = models.DateTimeField(null=True, blank=True)
    last_updated = models.DateTimeField(auto_now=True)
    data_collected_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "Base Azure Resource"
        verbose_name_plural = "Base Azure Resources"
        unique_together = ['resource_id', 'subscription', 'resource_type']
        indexes = [
            models.Index(fields=['resource_type', 'subscription']),
            models.Index(fields=['resource_name']),
            models.Index(fields=['state']),
        ]

    def __str__(self):
        return f"{self.resource_type}: {self.resource_name} ({self.subscription.subscription_name})"


class AzureTag(models.Model):
    """Tag model for Azure resource tags"""
    resource = models.ForeignKey(BaseAzureResource, on_delete=models.CASCADE, related_name='tags')
    key = models.CharField(max_length=255)
    value = models.CharField(max_length=255, blank=True)

    class Meta:
        verbose_name = "Azure Tag"
        verbose_name_plural = "Azure Tags"
        unique_together = ['resource', 'key']
        indexes = [
            models.Index(fields=['key', 'value']),
        ]

    def __str__(self):
        return f"{self.key}: {self.value}"


class VirtualMachine(BaseAzureResource):
    """Azure Virtual Machine specific model"""
    vm_size = models.CharField(max_length=50)
    os_type = models.CharField(max_length=20, blank=True)  # Windows/Linux
    os_version = models.CharField(max_length=100, blank=True)
    computer_name = models.CharField(max_length=64, blank=True)
    admin_username = models.CharField(max_length=64, blank=True)
    public_ip = models.GenericIPAddressField(null=True, blank=True)
    private_ip = models.GenericIPAddressField(null=True, blank=True)
    power_state = models.CharField(max_length=20, blank=True)

    class Meta:
        verbose_name = "Azure Virtual Machine"
        verbose_name_plural = "Azure Virtual Machines"


class StorageAccount(BaseAzureResource):
    """Azure Storage Account specific model"""
    account_type = models.CharField(max_length=50, blank=True)
    sku_name = models.CharField(max_length=50, blank=True)
    sku_tier = models.CharField(max_length=20, blank=True)
    access_tier = models.CharField(max_length=20, blank=True)
    https_traffic_only = models.BooleanField(default=True)
    allow_blob_public_access = models.BooleanField(default=False)

    class Meta:
        verbose_name = "Azure Storage Account"
        verbose_name_plural = "Azure Storage Accounts"


class AKSCluster(BaseAzureResource):
    """Azure Kubernetes Service specific model"""
    kubernetes_version = models.CharField(max_length=20, blank=True)
    dns_prefix = models.CharField(max_length=54, blank=True)
    fqdn = models.CharField(max_length=255, blank=True)
    node_count = models.IntegerField(null=True, blank=True)
    vm_size = models.CharField(max_length=50, blank=True)
    network_plugin = models.CharField(max_length=20, blank=True)

    class Meta:
        verbose_name = "Azure AKS Cluster"
        verbose_name_plural = "Azure AKS Clusters"


# Sample data for demonstration
class AzureSampleData:
    """Helper class to create sample Azure data"""
    
    @staticmethod
    def create_sample_data():
        from inventory.models import BusinessUnit
        
        # Get or create business units
        hq_bu, _ = BusinessUnit.objects.get_or_create(
            code='HQ',
            defaults={'name': 'Vernova CTO HQ', 'description': 'Corporate HQ'}
        )
        
        # Create sample Azure subscription
        subscription, created = AzureSubscription.objects.get_or_create(
            subscription_id='12345678-1234-1234-1234-123456789012',
            defaults={
                'subscription_name': 'GE Vernova Production',
                'tenant_id': '87654321-4321-4321-4321-210987654321',
                'business_unit': hq_bu,
                'is_active': True
            }
        )
        
        # Create sample resource groups
        rg_prod, _ = AzureResourceGroup.objects.get_or_create(
            name='rg-vernova-prod',
            subscription=subscription,
            defaults={'location': 'East US'}
        )
        
        rg_dev, _ = AzureResourceGroup.objects.get_or_create(
            name='rg-vernova-dev',
            subscription=subscription,
            defaults={'location': 'West US 2'}
        )
        
        # Create sample VMs
        vm_data = [
            {
                'resource_name': 'vm-web-prod-01',
                'vm_size': 'Standard_D2s_v3',
                'os_type': 'Linux',
                'os_version': 'Ubuntu 20.04',
                'resource_group': rg_prod,
                'state': 'Running'
            },
            {
                'resource_name': 'vm-db-prod-01',
                'vm_size': 'Standard_D4s_v3',
                'os_type': 'Windows',
                'os_version': 'Windows Server 2019',
                'resource_group': rg_prod,
                'state': 'Running'
            },
            {
                'resource_name': 'vm-test-dev-01',
                'vm_size': 'Standard_B2s',
                'os_type': 'Linux',
                'os_version': 'Ubuntu 22.04',
                'resource_group': rg_dev,
                'state': 'Stopped'
            }
        ]
        
        for vm_info in vm_data:
            vm, created = VirtualMachine.objects.get_or_create(
                resource_name=vm_info['resource_name'],
                subscription=subscription,
                defaults={
                    'resource_id': f"/subscriptions/{subscription.subscription_id}/resourceGroups/{vm_info['resource_group'].name}/providers/Microsoft.Compute/virtualMachines/{vm_info['resource_name']}",
                    'resource_type': 'VM',
                    'resource_group': vm_info['resource_group'],
                    'location': vm_info['resource_group'].location,
                    'state': vm_info['state'],
                    'vm_size': vm_info['vm_size'],
                    'os_type': vm_info['os_type'],
                    'os_version': vm_info['os_version']
                }
            )
            
            if created:
                # Add sample tags
                AzureTag.objects.create(resource=vm, key='Environment', value='Production' if 'prod' in vm_info['resource_name'] else 'Development')
                AzureTag.objects.create(resource=vm, key='Owner', value='Platform Team')
                AzureTag.objects.create(resource=vm, key='CostCenter', value='IT-001')
        
        # Create sample storage accounts
        storage_data = [
            {
                'resource_name': 'stvernovaprod001',
                'account_type': 'StorageV2',
                'sku_name': 'Standard_LRS',
                'resource_group': rg_prod,
                'state': 'Available'
            },
            {
                'resource_name': 'stvernovadev001',
                'account_type': 'StorageV2',
                'sku_name': 'Standard_GRS',
                'resource_group': rg_dev,
                'state': 'Available'
            }
        ]
        
        for storage_info in storage_data:
            storage, created = StorageAccount.objects.get_or_create(
                resource_name=storage_info['resource_name'],
                subscription=subscription,
                defaults={
                    'resource_id': f"/subscriptions/{subscription.subscription_id}/resourceGroups/{storage_info['resource_group'].name}/providers/Microsoft.Storage/storageAccounts/{storage_info['resource_name']}",
                    'resource_type': 'STORAGE',
                    'resource_group': storage_info['resource_group'],
                    'location': storage_info['resource_group'].location,
                    'state': storage_info['state'],
                    'account_type': storage_info['account_type'],
                    'sku_name': storage_info['sku_name']
                }
            )
        
        return f"Created sample Azure data: {subscription.subscription_name}"
