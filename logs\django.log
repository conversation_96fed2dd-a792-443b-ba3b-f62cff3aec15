Watching for file changes with StatReloader
"GET / HTTP/1.1" 302 0
"GET /auth/login/?next=/ HTTP/1.1" 200 7704
"GET /static/css/custom.css HTTP/1.1" 200 5434
"GET /static/images/ge_vernova.svg HTTP/1.1" 200 330
"GET /static/images/GEV-e06a174f.png HTTP/1.1" 200 167
"GET / HTTP/1.1" 302 0
"GET / HTTP/1.1" 302 0
"GET /auth/login/?next=/ HTTP/1.1" 200 7704
"GET /static/css/custom.css HTTP/1.1" 304 0
"GET /static/images/ge_vernova.svg HTTP/1.1" 304 0
"GET /static/images/GEV-e06a174f.png HTTP/1.1" 304 0
"POST /auth/login/?next=/ HTTP/1.1" 302 0
"GET / HTTP/1.1" 200 25588
"GET /static/js/custom.js HTTP/1.1" 200 9325
Internal Server Error: /resources/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: inventory/resource_list.html, inventory/baseresource_list.html
"GET /resources/ HTTP/1.1" 500 92088
Not Found: /favicon.ico
"GET /favicon.ico HTTP/1.1" 404 4554
"GET / HTTP/1.1" 200 25588
- Broken pipe from ('127.0.0.1', 49977)
"GET / HTTP/1.1" 200 25588
"GET / HTTP/1.1" 200 25588
Internal Server Error: /auth/profile/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: authentication/profile.html
"GET /auth/profile/ HTTP/1.1" 500 89138
"GET /static/images/GEV-e06a174f.png HTTP/1.1" 304 0
"GET /?ajax=1 HTTP/1.1" 200 25588
"GET / HTTP/1.1" 200 25588
"GET /static/images/ge_vernova.svg HTTP/1.1" 304 0
"GET /?ajax=1 HTTP/1.1" 200 25588
"GET / HTTP/1.1" 200 25588
"GET /static/css/custom.css HTTP/1.1" 304 0
"GET /static/js/custom.js HTTP/1.1" 304 0
"GET /?ajax=1 HTTP/1.1" 200 25588
"GET / HTTP/1.1" 200 25588
"GET /static/images/GEV-e06a174f.png HTTP/1.1" 304 0
"GET / HTTP/1.1" 200 25588
"GET /?ajax=1 HTTP/1.1" 200 25588
"GET /static/images/ge_vernova.svg HTTP/1.1" 304 0
"GET / HTTP/1.1" 200 25588
"GET /?ajax=1 HTTP/1.1" 200 25588
"GET /?ajax=1 HTTP/1.1" 200 25588
"GET / HTTP/1.1" 200 25588
"GET /static/css/custom.css HTTP/1.1" 304 0
"GET /static/images/GEV-e06a174f.png HTTP/1.1" 304 0
"GET /static/js/custom.js HTTP/1.1" 304 0
"GET /?ajax=1 HTTP/1.1" 200 25588
"GET / HTTP/1.1" 200 25588
"GET /static/images/ge_vernova.svg HTTP/1.1" 304 0
"GET /?ajax=1 HTTP/1.1" 200 25588
"GET / HTTP/1.1" 200 25588
"GET /?ajax=1 HTTP/1.1" 200 25588
"GET / HTTP/1.1" 200 25588
"GET /?ajax=1 HTTP/1.1" 200 25588
"GET / HTTP/1.1" 200 25588
"GET /static/images/GEV-e06a174f.png HTTP/1.1" 304 0
"GET /?ajax=1 HTTP/1.1" 200 25588
"GET / HTTP/1.1" 200 25588
"GET /static/css/custom.css HTTP/1.1" 304 0
"GET /static/js/custom.js HTTP/1.1" 304 0
"GET /static/images/ge_vernova.svg HTTP/1.1" 304 0
"GET / HTTP/1.1" 200 25588
"GET /resources/ HTTP/1.1" 200 117661
"GET /resources/?search=s&type=&account=&region=&state= HTTP/1.1" 200 117680
"GET /resources/?search=&type=&account=&region=&state= HTTP/1.1" 200 117661
"GET /resources/?search=pc&type=&account=&region=&state= HTTP/1.1" 200 53567
"GET /resources/?search=pc-&type=&account=&region=&state= HTTP/1.1" 200 53568
"GET /resources/?search=pc-f&type=&account=&region=&state= HTTP/1.1" 200 28988
"GET /resources/?search=pc-&type=&account=&region=&state= HTTP/1.1" 200 53568
"GET /resources/?search=&type=&account=&region=&state= HTTP/1.1" 200 117661
"GET /resources/?search=g&type=&account=&region=&state= HTTP/1.1" 200 65525
"GET /resources/?search=&type=&account=&region=&state= HTTP/1.1" 200 117661
"GET /resource/89/ HTTP/1.1" 200 11524
"GET /resources/ HTTP/1.1" 200 117661
"GET /resources/?search=&type=EC2&account=&region=&state= HTTP/1.1" 200 99911
"GET /resource/83/ HTTP/1.1" 200 13924
"GET /resources/ HTTP/1.1" 200 117661
"GET /static/images/GEV-e06a174f.png HTTP/1.1" 304 0
"GET /resources/ HTTP/1.1" 200 117661
"GET /resources/ HTTP/1.1" 200 117661
"GET / HTTP/1.1" 200 25588
"GET /auth/profile/ HTTP/1.1" 200 8297
"GET /static/images/ge_vernova.svg HTTP/1.1" 304 0
"GET / HTTP/1.1" 200 25588
"GET /static/js/custom.js HTTP/1.1" 304 0
Internal Server Error: /search/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: inventory/resource_search.html
"GET /search/ HTTP/1.1" 500 89136
"GET /export/ HTTP/1.1" 200 14461
"GET / HTTP/1.1" 200 25588
"GET /?ajax=1 HTTP/1.1" 200 25588
"GET /static/css/custom.css HTTP/1.1" 304 0
Watching for file changes with StatReloader
"GET / HTTP/1.1" 200 25588
"GET /static/images/GEV-e06a174f.png HTTP/1.1" 304 0
"GET /admin HTTP/1.1" 301 0
"GET /admin/ HTTP/1.1" 200 21727
"GET /static/admin/css/base.css HTTP/1.1" 200 21207
"GET /static/admin/js/nav_sidebar.js HTTP/1.1" 200 3063
"GET /static/admin/css/dark_mode.css HTTP/1.1" 200 2929
"GET /static/admin/css/dashboard.css HTTP/1.1" 200 441
"GET /static/admin/css/nav_sidebar.css HTTP/1.1" 200 2694
"GET /static/admin/css/responsive.css HTTP/1.1" 200 18533
"GET /static/admin/js/theme.js HTTP/1.1" 200 1943
"GET /static/admin/img/icon-changelink.svg HTTP/1.1" 200 380
"GET /static/admin/img/icon-viewlink.svg HTTP/1.1" 200 581
"GET /static/admin/img/icon-addlink.svg HTTP/1.1" 200 331
C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\inventory\views.py changed, reloading.
Watching for file changes with StatReloader
"GET /admin/authentication/samlconfiguration/ HTTP/1.1" 200 20243
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /static/admin/css/changelists.css HTTP/1.1" 200 6566
"GET /static/admin/js/urlify.js HTTP/1.1" 200 7887
"GET /static/admin/js/prepopulate.js HTTP/1.1" 200 1531
"GET /static/admin/js/vendor/xregexp/xregexp.js HTTP/1.1" 200 232381
"GET /static/admin/js/jquery.init.js HTTP/1.1" 200 347
"GET /static/admin/img/search.svg HTTP/1.1" 200 458
"GET /static/admin/js/core.js HTTP/1.1" 200 5682
"GET /static/admin/js/admin/RelatedObjectLookups.js HTTP/1.1" 200 8943
"GET /static/admin/js/actions.js HTTP/1.1" 200 7872
"GET /static/admin/js/vendor/jquery/jquery.js HTTP/1.1" 200 292458
"GET /static/admin/js/filters.js HTTP/1.1" 200 978
"GET /static/admin/img/tooltag-add.svg HTTP/1.1" 200 331
"GET /admin/authentication/samlconfiguration/add/ HTTP/1.1" 200 23385
"GET /static/admin/js/prepopulate_init.js HTTP/1.1" 200 586
"GET /static/admin/css/forms.css HTTP/1.1" 200 9047
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /static/admin/js/change_form.js HTTP/1.1" 200 606
"GET /static/admin/css/widgets.css HTTP/1.1" 200 11900
C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\inventory\views.py changed, reloading.
Watching for file changes with StatReloader
"GET /admin/authentication/usergroup/ HTTP/1.1" 200 22591
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /admin/authentication/usergroup/1/change/ HTTP/1.1" 200 20531
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /admin/auth/user/ HTTP/1.1" 200 23413
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /static/admin/img/icon-no.svg HTTP/1.1" 200 560
"GET /static/admin/img/icon-yes.svg HTTP/1.1" 200 436
"GET /static/admin/img/sorting-icons.svg HTTP/1.1" 200 1097
"GET /admin/auth/user/1/change/ HTTP/1.1" 200 55067
"GET /static/admin/js/calendar.js HTTP/1.1" 200 8466
"GET /static/admin/js/admin/DateTimeShortcuts.js HTTP/1.1" 200 19319
"GET /static/admin/js/inlines.js HTTP/1.1" 200 15526
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /static/admin/js/SelectBox.js HTTP/1.1" 200 4530
"GET /static/admin/js/SelectFilter2.js HTTP/1.1" 200 15292
"GET /static/admin/img/inline-delete.svg HTTP/1.1" 200 560
"GET /static/admin/img/selector-icons.svg HTTP/1.1" 200 3291
"GET /static/admin/img/icon-calendar.svg HTTP/1.1" 200 1086
"GET /static/admin/img/icon-unknown-alt.svg HTTP/1.1" 200 655
"GET /static/admin/img/icon-clock.svg HTTP/1.1" 200 677
"GET /static/admin/img/icon-unknown.svg HTTP/1.1" 200 655
"POST /admin/auth/user/1/change/ HTTP/1.1" 302 0
"GET /admin/auth/user/ HTTP/1.1" 200 23606
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
Forbidden (CSRF token from POST incorrect.): /auth/login/
"POST /auth/login/?next=/ HTTP/1.1" 403 2518
"GET /auth/login/?next=/ HTTP/1.1" 302 0
"GET /auth/logout/ HTTP/1.1" 302 0
"GET /auth/login/ HTTP/1.1" 200 7704
"GET /static/css/custom.css HTTP/1.1" 304 0
"GET /static/images/ge_vernova.svg HTTP/1.1" 304 0
"POST /auth/login/ HTTP/1.1" 302 0
"GET / HTTP/1.1" 200 27509
"GET /static/images/GEV-e06a174f.png HTTP/1.1" 304 0
"GET /static/js/custom.js HTTP/1.1" 304 0
"GET /jobs/ HTTP/1.1" 200 15397
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4545
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4545
Internal Server Error: /jobs/jobs/create/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: jobs/job_create.html
"GET /jobs/jobs/create/ HTTP/1.1" 500 89852
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4545
Internal Server Error: /aws-session/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: aws_session/session_list.html
"GET /aws-session/ HTTP/1.1" 500 89978
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4545
C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\jobs\views.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\jobs\views.py changed, reloading.
Watching for file changes with StatReloader
Internal Server Error: /auth/users/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: authentication/user_list.html, auth/user_list.html
"GET /auth/users/ HTTP/1.1" 500 92703
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4545
Internal Server Error: /jobs/jobs/create/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: jobs/job_create.html
"GET /jobs/jobs/create/ HTTP/1.1" 500 89852
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4545
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4545
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4545
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4545
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4545
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4545
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4545
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4545
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4545
C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\jobs\views.py changed, reloading.
Watching for file changes with StatReloader
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4545
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4545
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4545
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4545
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4545
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4545
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4545
Internal Server Error: /jobs/jobs/create/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: jobs/job_create.html
"GET /jobs/jobs/create/ HTTP/1.1" 500 89852
"GET /static/images/GEV-e06a174f.png HTTP/1.1" 304 0
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4545
"GET / HTTP/1.1" 200 27509
"GET /static/css/custom.css HTTP/1.1" 304 0
"GET /static/images/ge_vernova.svg HTTP/1.1" 304 0
"GET /static/js/custom.js HTTP/1.1" 304 0
"GET /resources/ HTTP/1.1" 200 119268
"GET / HTTP/1.1" 200 27509
C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\jobs\views.py changed, reloading.
Watching for file changes with StatReloader
"GET /?ajax=1 HTTP/1.1" 200 27509
"GET / HTTP/1.1" 200 27509
"GET /?ajax=1 HTTP/1.1" 200 27509
"GET / HTTP/1.1" 200 27509
"GET / HTTP/1.1" 200 27509
"GET /?ajax=1 HTTP/1.1" 200 27509
"GET / HTTP/1.1" 200 27509
"GET /?ajax=1 HTTP/1.1" 200 27509
Watching for file changes with StatReloader
"GET / HTTP/1.1" 200 27509
"GET /static/images/ge_vernova.svg HTTP/1.1" 304 0
"GET /static/images/GEV-e06a174f.png HTTP/1.1" 304 0
"GET /?ajax=1 HTTP/1.1" 200 27509
"GET / HTTP/1.1" 200 27509
"GET /static/images/ge_vernova.svg HTTP/1.1" 304 0
"GET / HTTP/1.1" 200 27509
- Broken pipe from ('127.0.0.1', 50937)
"GET /static/js/custom.js HTTP/1.1" 304 0
"GET / HTTP/1.1" 200 27509
- Broken pipe from ('127.0.0.1', 51035)
"GET /static/css/custom.css HTTP/1.1" 304 0
"GET /static/js/custom.js HTTP/1.1" 304 0
"GET / HTTP/1.1" 200 27509
"GET /?ajax=1 HTTP/1.1" 200 27509
"GET /static/css/custom.css HTTP/1.1" 304 0
"GET /?ajax=1 HTTP/1.1" 200 27509
"GET / HTTP/1.1" 200 27509
"GET /?ajax=1 HTTP/1.1" 200 27509
"GET / HTTP/1.1" 200 27509
"GET /?ajax=1 HTTP/1.1" 200 27509
"GET / HTTP/1.1" 200 27509
"GET /?ajax=1 HTTP/1.1" 200 27509
"GET / HTTP/1.1" 200 27509
"GET /?ajax=1 HTTP/1.1" 200 27509
"GET / HTTP/1.1" 200 27509
"GET /?ajax=1 HTTP/1.1" 200 27509
"GET / HTTP/1.1" 200 27509
"GET / HTTP/1.1" 200 27509
"GET /?ajax=1 HTTP/1.1" 200 27509
"GET /?ajax=1 HTTP/1.1" 200 27509
"GET / HTTP/1.1" 200 27509
"GET /?ajax=1 HTTP/1.1" 200 27509
"GET / HTTP/1.1" 200 27509
"GET /static/images/GEV-e06a174f.png HTTP/1.1" 304 0
"GET /?ajax=1 HTTP/1.1" 200 27509
"GET / HTTP/1.1" 200 27509
"GET /static/images/ge_vernova.svg HTTP/1.1" 304 0
"GET / HTTP/1.1" 200 27509
"GET /?ajax=1 HTTP/1.1" 200 27509
"GET /static/images/ge_vernova.svg HTTP/1.1" 304 0
"GET / HTTP/1.1" 200 27509
- Broken pipe from ('127.0.0.1', 51102)
"GET /?ajax=1 HTTP/1.1" 200 27509
"GET / HTTP/1.1" 200 27509
"GET /?ajax=1 HTTP/1.1" 200 27509
"GET / HTTP/1.1" 200 27509
"GET /static/js/custom.js HTTP/1.1" 304 0
"GET / HTTP/1.1" 200 27509
- Broken pipe from ('127.0.0.1', 51217)
"GET /static/css/custom.css HTTP/1.1" 304 0
"GET /static/js/custom.js HTTP/1.1" 304 0
"GET /?ajax=1 HTTP/1.1" 200 27509
"GET / HTTP/1.1" 200 27509
"GET /static/css/custom.css HTTP/1.1" 304 0
"GET / HTTP/1.1" 200 27509
"GET /?ajax=1 HTTP/1.1" 200 27509
"GET /?ajax=1 HTTP/1.1" 200 27509
"GET / HTTP/1.1" 200 27509
"GET /?ajax=1 HTTP/1.1" 200 27509
"GET / HTTP/1.1" 200 27509
"GET /?ajax=1 HTTP/1.1" 200 27509
"GET / HTTP/1.1" 200 27509
"GET /?ajax=1 HTTP/1.1" 200 27509
"GET / HTTP/1.1" 200 27509
"GET /?ajax=1 HTTP/1.1" 200 27509
"GET / HTTP/1.1" 200 27509
"GET / HTTP/1.1" 200 27509
"GET /static/images/GEV-e06a174f.png HTTP/1.1" 304 0
- Broken pipe from ('127.0.0.1', 51275)
"GET /?ajax=1 HTTP/1.1" 200 27509
"GET / HTTP/1.1" 200 27509
"GET /?ajax=1 HTTP/1.1" 200 27509
"GET / HTTP/1.1" 200 27509
"GET / HTTP/1.1" 200 27509
- Broken pipe from ('127.0.0.1', 51306)
"GET /static/images/ge_vernova.svg HTTP/1.1" 304 0
"GET /?ajax=1 HTTP/1.1" 200 27509
"GET / HTTP/1.1" 200 27509
"GET /static/images/ge_vernova.svg HTTP/1.1" 304 0
"GET /?ajax=1 HTTP/1.1" 200 27509
"GET / HTTP/1.1" 200 27509
"GET /?ajax=1 HTTP/1.1" 200 27509
"GET / HTTP/1.1" 200 27509
"GET /?ajax=1 HTTP/1.1" 200 27509
"GET / HTTP/1.1" 200 27509
"GET /?ajax=1 HTTP/1.1" 200 27509
"GET / HTTP/1.1" 200 27509
"GET /?ajax=1 HTTP/1.1" 200 27509
"GET / HTTP/1.1" 200 27509
"GET /static/js/custom.js HTTP/1.1" 304 0
"GET / HTTP/1.1" 200 27509
- Broken pipe from ('127.0.0.1', 51342)
"GET /static/css/custom.css HTTP/1.1" 304 0
"GET /static/js/custom.js HTTP/1.1" 304 0
"GET /?ajax=1 HTTP/1.1" 200 27509
"GET / HTTP/1.1" 200 27509
"GET /static/css/custom.css HTTP/1.1" 304 0
"GET / HTTP/1.1" 200 27509
- Broken pipe from ('127.0.0.1', 51320)
"GET /?ajax=1 HTTP/1.1" 200 27509
"GET / HTTP/1.1" 200 27509
"GET /?ajax=1 HTTP/1.1" 200 27509
"GET / HTTP/1.1" 200 27509
"GET / HTTP/1.1" 200 27509
- Broken pipe from ('127.0.0.1', 51400)
"GET /static/images/GEV-e06a174f.png HTTP/1.1" 304 0
"GET /?ajax=1 HTTP/1.1" 200 27509
"GET / HTTP/1.1" 200 27509
"GET / HTTP/1.1" 200 27509
- Broken pipe from ('127.0.0.1', 51471)
"GET / HTTP/1.1" 200 27509
- Broken pipe from ('127.0.0.1', 51490)
"GET /?ajax=1 HTTP/1.1" 200 27509
"GET / HTTP/1.1" 200 27509
"GET /static/images/ge_vernova.svg HTTP/1.1" 304 0
"GET / HTTP/1.1" 200 27509
- Broken pipe from ('127.0.0.1', 51511)
"GET /static/images/ge_vernova.svg HTTP/1.1" 304 0
"GET /?ajax=1 HTTP/1.1" 200 27509
"GET / HTTP/1.1" 200 27509
"GET /jobs/ HTTP/1.1" 200 15397
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4545
"GET /jobs/jobs/create/ HTTP/1.1" 200 19857
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4545
"GET /?ajax=1 HTTP/1.1" 200 27509
"GET / HTTP/1.1" 200 27509
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4545
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4545
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4545
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4545
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4545
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4545
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4545
"GET / HTTP/1.1" 200 27509
- Broken pipe from ('127.0.0.1', 51423)
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4545
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4545
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4545
"POST /jobs/jobs/create/ HTTP/1.1" 302 0
Internal Server Error: /jobs/jobs/0cb50575-3a4c-4721-a426-8b7233b85d65/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: jobs/job_detail.html
"GET /jobs/jobs/0cb50575-3a4c-4721-a426-8b7233b85d65/ HTTP/1.1" 500 90607
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4545
"GET /jobs/ HTTP/1.1" 200 15703
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4545
Internal Server Error: /jobs/schedule/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: jobs/schedule_list.html, jobs/jobschedule_list.html
"GET /jobs/schedule/ HTTP/1.1" 500 92745
"GET /?ajax=1 HTTP/1.1" 200 27509
"GET / HTTP/1.1" 200 27509
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4545
Internal Server Error: /jobs/executions/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: jobs/execution_list.html, jobs/jobexecution_list.html
"GET /jobs/executions/ HTTP/1.1" 500 92799
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4545
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4545
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4545
"GET / HTTP/1.1" 200 27509
- Broken pipe from ('127.0.0.1', 51601)
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4545
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4545
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4545
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4545
"GET / HTTP/1.1" 200 27509
"GET /?ajax=1 HTTP/1.1" 200 27509
"GET /static/js/custom.js HTTP/1.1" 304 0
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4545
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4545
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4545
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4545
Internal Server Error: /aws-session/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: aws_session/session_list.html
"GET /aws-session/ HTTP/1.1" 500 89978
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4545
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4545
"GET / HTTP/1.1" 200 27509
"GET /static/css/custom.css HTTP/1.1" 304 0
- Broken pipe from ('127.0.0.1', 51746)
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4545
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4545
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4545
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4545
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4545
"GET /?ajax=1 HTTP/1.1" 200 27509
"GET / HTTP/1.1" 200 27509
"GET /static/images/GEV-e06a174f.png HTTP/1.1" 304 0
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4545
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4545
- Broken pipe from ('127.0.0.1', 51837)
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4545
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4545
"GET /?ajax=1 HTTP/1.1" 200 27509
"GET / HTTP/1.1" 200 27509
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4545
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4545
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4545
C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\cloud_inventory\settings.py changed, reloading.
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4545
C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\cloud_inventory\settings.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4545
"GET / HTTP/1.1" 200 27509
"GET /?ajax=1 HTTP/1.1" 200 27509
"GET /static/images/ge_vernova.svg HTTP/1.1" 304 0
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4545
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4545
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4545
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4545
C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\cloud_inventory\urls.py changed, reloading.
C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\cloud_inventory\urls.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
"GET / HTTP/1.1" 200 27509
"GET /?ajax=1 HTTP/1.1" 200 27509
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
"GET /?ajax=1 HTTP/1.1" 200 27509
"GET / HTTP/1.1" 200 27509
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
"GET / HTTP/1.1" 200 27509
"GET /?ajax=1 HTTP/1.1" 200 27509
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
- Broken pipe from ('127.0.0.1', 52006)
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
"GET / HTTP/1.1" 200 27509
"GET /?ajax=1 HTTP/1.1" 200 27509
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
"GET /?ajax=1 HTTP/1.1" 200 27509
"GET / HTTP/1.1" 200 27509
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
"GET /?ajax=1 HTTP/1.1" 200 36815
"GET / HTTP/1.1" 200 36815
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
"GET /?ajax=1 HTTP/1.1" 200 36815
"GET / HTTP/1.1" 200 36815
"GET /static/js/custom.js HTTP/1.1" 304 0
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
"GET /?ajax=1 HTTP/1.1" 200 36815
"GET / HTTP/1.1" 200 36815
"GET /static/css/custom.css HTTP/1.1" 304 0
"GET /static/images/GEV-e06a174f.png HTTP/1.1" 304 0
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
"GET /?ajax=1 HTTP/1.1" 200 36815
"GET / HTTP/1.1" 200 36815
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Watching for file changes with StatReloader
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
"GET /?ajax=1 HTTP/1.1" 200 36815
"GET / HTTP/1.1" 200 36815
"GET /static/images/ge_vernova.svg HTTP/1.1" 304 0
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
"GET / HTTP/1.1" 200 36815
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
"GET /resources/ec2/ HTTP/1.1" 200 110810
"GET /?ajax=1 HTTP/1.1" 200 36815
"GET / HTTP/1.1" 200 36815
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
"GET /resources/s3/ HTTP/1.1" 200 74502
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
"GET /resources/lambda/ HTTP/1.1" 200 72467
"GET /resources/eks/ HTTP/1.1" 200 26091
"GET /resources/ecs/ HTTP/1.1" 200 26091
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
"GET /azure/vm/ HTTP/1.1" 200 27304
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
"GET /?ajax=1 HTTP/1.1" 200 36815
"GET / HTTP/1.1" 200 36815
Internal Server Error: /azure/resource/3/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: azure/resource_detail.html
"GET /azure/resource/3/ HTTP/1.1" 500 90082
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
"GET /operations/templates/ HTTP/1.1" 200 33956
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Internal Server Error: /operations/adhoc/create/
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\template\response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\template\response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\augment-projects\cloud_central_SS\venv\Lib\site-packages\django\template\loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: operations/adhoc_create.html
"GET /operations/adhoc/create/?template=fbfbc418-4a13-446c-accb-aec6d48ca595 HTTP/1.1" 500 90730
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
"GET /?ajax=1 HTTP/1.1" 200 36815
"GET / HTTP/1.1" 200 36815
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
"GET /jobs/jobs/ HTTP/1.1" 200 40318
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
"GET /?ajax=1 HTTP/1.1" 200 36815
"GET / HTTP/1.1" 200 36815
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
"GET /jobs/jobs/create/ HTTP/1.1" 200 29163
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
"GET /jobs/jobs/ HTTP/1.1" 200 40318
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
"GET /jobs/jobs/?type=account_specific&search= HTTP/1.1" 200 25594
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
"GET /jobs/jobs/?type=&search= HTTP/1.1" 200 40318
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
"GET /?ajax=1 HTTP/1.1" 200 36815
"GET / HTTP/1.1" 200 36815
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
"GET / HTTP/1.1" 200 36815
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
"GET /?ajax=1 HTTP/1.1" 200 36815
- Broken pipe from ('127.0.0.1', 52560)
"GET / HTTP/1.1" 200 36815
"GET /?ajax=1 HTTP/1.1" 200 36815
"GET / HTTP/1.1" 200 36815
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
"GET /operations/templates/ HTTP/1.1" 200 33956
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
"GET /?ajax=1 HTTP/1.1" 200 36815
"GET / HTTP/1.1" 200 36815
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
"GET /?ajax=1 HTTP/1.1" 200 36815
"GET / HTTP/1.1" 200 36815
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
"GET / HTTP/1.1" 200 36777
"GET /?ajax=1 HTTP/1.1" 200 36777
"GET /static/images/GEV-e06a174f.png HTTP/1.1" 304 0
"GET /static/js/custom.js HTTP/1.1" 304 0
"GET /static/images/GE-Vernova-E.png HTTP/1.1" 200 31919
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
"GET / HTTP/1.1" 200 36777
"GET /?ajax=1 HTTP/1.1" 200 36777
"GET /static/css/custom.css HTTP/1.1" 304 0
"GET /static/images/GE-Vernova-E.png HTTP/1.1" 304 0
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
"GET /operations/templates/ HTTP/1.1" 200 33958
"GET /operations/templates/ HTTP/1.1" 200 33958
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
"GET /?ajax=1 HTTP/1.1" 200 36887
"GET / HTTP/1.1" 200 36887
"GET /static/images/GE-Vernova-E.png HTTP/1.1" 304 0
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
"GET /operations/templates/ HTTP/1.1" 200 33967
"GET /static/images/GE-Vernova-E.png HTTP/1.1" 304 0
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
"GET / HTTP/1.1" 200 36896
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
"GET /?ajax=1 HTTP/1.1" 200 36896
"GET / HTTP/1.1" 200 36896
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
"GET /?ajax=1 HTTP/1.1" 200 36896
"GET / HTTP/1.1" 200 36896
"GET /static/images/GE-Vernova-E.png HTTP/1.1" 304 0
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
"GET /?ajax=1 HTTP/1.1" 200 36906
"GET / HTTP/1.1" 200 36906
"GET /static/images/GE-Vernova-E.png HTTP/1.1" 304 0
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
"GET /?ajax=1 HTTP/1.1" 200 36906
"GET / HTTP/1.1" 200 36906
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
"GET / HTTP/1.1" 200 36906
- Broken pipe from ('127.0.0.1', 52564)
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
"GET / HTTP/1.1" 200 36906
"GET /static/images/GE-Vernova-E.png HTTP/1.1" 304 0
"GET / HTTP/1.1" 200 36906
"GET / HTTP/1.1" 200 36906
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
"GET / HTTP/1.1" 200 36906
"GET /?ajax=1 HTTP/1.1" 200 36906
"GET /static/images/GE-Vernova-E.png HTTP/1.1" 304 0
Not Found: /ws/jobs/
"GET /ws/jobs/ HTTP/1.1" 404 4762
"GET / HTTP/1.1" 200 36906
- Broken pipe from ('127.0.0.1', 52860)
