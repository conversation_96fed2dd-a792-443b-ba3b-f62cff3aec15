{% extends 'base.html' %}
{% load static %}

{% block title %}Jobs - Cloud Central SS{% endblock %}

{% block extra_css %}
<style>
    .job-card {
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 1.5rem;
        margin-bottom: 1rem;
        background: white;
        transition: box-shadow 0.2s;
    }
    .job-card:hover {
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }
    .job-status {
        font-size: 0.875rem;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
    }
    .job-type-badge {
        background: #005E60;
        color: white;
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
    }
    .run-job-btn {
        background: #28a745;
        border: none;
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 4px;
        cursor: pointer;
        transition: background 0.2s;
    }
    .run-job-btn:hover {
        background: #218838;
    }
    .run-job-btn:disabled {
        background: #6c757d;
        cursor: not-allowed;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2><i class="fas fa-tasks me-2"></i>{{ page_title|default:"Data Collection Jobs" }}</h2>
                    {% if page_description %}
                        <p class="text-muted mb-0">{{ page_description }}</p>
                    {% endif %}
                </div>
                <div class="d-flex gap-2">
                    {% if current_filters.category == 'inventory' %}
                        <a href="{% url 'jobs:job_create' %}" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>Create Collection Job
                        </a>
                        <a href="{% url 'jobs:execution_list' %}?category=inventory" class="btn btn-outline-secondary">
                            <i class="fas fa-history me-1"></i>View History
                        </a>
                    {% else %}
                        <a href="{% url 'jobs:job_create' %}" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>Create Job
                        </a>
                        <a href="{% url 'jobs:execution_list' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-history me-1"></i>View Executions
                        </a>
                    {% endif %}
                </div>
            </div>

            <!-- Filters -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <form method="get" class="d-flex gap-2">
                        {% if current_filters.category %}
                            <input type="hidden" name="category" value="{{ current_filters.category }}">
                        {% endif %}
                        <select name="type" class="form-select" onchange="this.form.submit()">
                            <option value="">All Job Types</option>
                            {% for type_code, type_name in job_types %}
                                <option value="{{ type_code }}" {% if current_filters.type == type_code %}selected{% endif %}>
                                    {{ type_name }}
                                </option>
                            {% endfor %}
                        </select>
                        <input type="text" name="search" class="form-control" placeholder="Search jobs..."
                               value="{{ current_filters.search }}">
                        <button type="submit" class="btn btn-outline-primary">
                            <i class="fas fa-search"></i>
                        </button>
                    </form>
                </div>
            </div>

            <!-- Jobs List -->
            {% if jobs %}
                <div class="row">
                    {% for job in jobs %}
                        <div class="col-md-6 col-lg-4">
                            <div class="job-card">
                                <div class="d-flex justify-content-between align-items-start mb-3">
                                    <h5 class="mb-0">
                                        <a href="{% url 'jobs:job_detail' job.pk %}" class="text-decoration-none">
                                            {{ job.name }}
                                        </a>
                                    </h5>
                                    <span class="job-type-badge">{{ job.get_job_type_display }}</span>
                                </div>

                                <p class="text-muted mb-3">{{ job.description|truncatechars:100 }}</p>

                                <div class="mb-3">
                                    <small class="text-muted">
                                        <i class="fas fa-user me-1"></i>{{ job.created_by.username }}
                                        <span class="ms-3">
                                            <i class="fas fa-calendar me-1"></i>{{ job.created_at|date:"M d, Y" }}
                                        </span>
                                    </small>
                                </div>

                                <div class="mb-3">
                                    <small class="text-muted">
                                        <i class="fas fa-server me-1"></i>{{ job.aws_session.name }}
                                        <span class="ms-3">
                                            <i class="fas fa-cogs me-1"></i>{{ job.resource_types|length }} resource types
                                        </span>
                                    </small>
                                </div>

                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="btn-group" role="group">
                                        <a href="{% url 'jobs:job_detail' job.pk %}" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{% url 'jobs:job_edit' job.pk %}" class="btn btn-sm btn-outline-secondary">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </div>

                                    <button type="button" class="run-job-btn" onclick="runJob('{{ job.pk }}', '{{ job.name }}')">
                                        <i class="fas fa-play me-1"></i>Run Now
                                    </button>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>

                <!-- Pagination -->
                {% if is_paginated %}
                    <nav aria-label="Jobs pagination" class="mt-4">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1{% if current_filters.type %}&type={{ current_filters.type }}{% endif %}{% if current_filters.search %}&search={{ current_filters.search }}{% endif %}">First</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if current_filters.type %}&type={{ current_filters.type }}{% endif %}{% if current_filters.search %}&search={{ current_filters.search }}{% endif %}">Previous</a>
                                </li>
                            {% endif %}

                            <li class="page-item active">
                                <span class="page-link">
                                    Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                                </span>
                            </li>

                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if current_filters.type %}&type={{ current_filters.type }}{% endif %}{% if current_filters.search %}&search={{ current_filters.search }}{% endif %}">Next</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if current_filters.type %}&type={{ current_filters.type }}{% endif %}{% if current_filters.search %}&search={{ current_filters.search }}{% endif %}">Last</a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-tasks fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">No Jobs Found</h4>
                    <p class="text-muted">Create your first data collection job to get started.</p>
                    <a href="{% url 'jobs:job_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>Create Job
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Run Job Modal -->
<div class="modal fade" id="runJobModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Run Job</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to run the job "<span id="jobNameSpan"></span>"?</p>
                <p class="text-muted">This will start a new execution and collect data from the configured AWS accounts.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="confirmRunBtn">
                    <i class="fas fa-play me-1"></i>Run Job
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentJobId = null;

function runJob(jobId, jobName) {
    currentJobId = jobId;
    document.getElementById('jobNameSpan').textContent = jobName;

    const modal = new bootstrap.Modal(document.getElementById('runJobModal'));
    modal.show();
}

document.getElementById('confirmRunBtn').addEventListener('click', function() {
    if (!currentJobId) return;

    const btn = this;
    const originalText = btn.innerHTML;

    // Show loading state
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Starting...';
    btn.disabled = true;

    // Make API call to run job
    fetch(`/jobs/api/jobs/${currentJobId}/run/`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            // Close modal
            bootstrap.Modal.getInstance(document.getElementById('runJobModal')).hide();

            // Show success message
            showAlert('success', `Job started successfully! Execution ID: ${data.execution_id}`);

            // Optionally redirect to execution detail
            setTimeout(() => {
                window.location.href = `/jobs/executions/${data.execution_id}/`;
            }, 2000);
        } else {
            showAlert('danger', `Failed to start job: ${data.error}`);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', 'An error occurred while starting the job.');
    })
    .finally(() => {
        // Reset button
        btn.innerHTML = originalText;
        btn.disabled = false;
    });
});

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // Insert at top of container
    const container = document.querySelector('.container-fluid .row .col-12');
    container.insertBefore(alertDiv, container.firstChild);

    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// Add CSRF token to all forms
document.addEventListener('DOMContentLoaded', function() {
    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]');
    if (!csrfToken) {
        // Add CSRF token if not present
        const token = document.createElement('input');
        token.type = 'hidden';
        token.name = 'csrfmiddlewaretoken';
        token.value = '{{ csrf_token }}';
        document.body.appendChild(token);
    }
});
</script>
{% endblock %}
