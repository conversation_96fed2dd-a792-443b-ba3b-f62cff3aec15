<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Cloud Central SS - AWS Resource Inventory{% endblock %}</title>

    <!-- Favicon -->
    {% load static %}
    <link rel="icon" type="image/png" href="{% static 'images/GEV-e06a174f.png' %}">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="{% static 'css/custom.css' %}" rel="stylesheet">

    <style>
        body {
            padding-top: 60px;
        }

        .sidebar {
            position: fixed;
            top: 60px;
            left: -280px;
            width: 280px;
            height: calc(100vh - 60px);
            background: #f8f9fa;
            border-right: 1px solid #dee2e6;
            transition: left 0.3s ease;
            z-index: 1000;
            overflow-y: auto;
        }

        .sidebar.show {
            left: 0;
        }

        .sidebar-header {
            padding: 1rem;
            background: #005E60;
            color: white;
            font-weight: 600;
        }

        .sidebar-content {
            padding: 0;
        }

        .sidebar-section {
            border-bottom: 1px solid #dee2e6;
        }

        .sidebar-item, .sidebar-header-item {
            display: flex;
            align-items: center;
            padding: 0.75rem 1rem;
            color: #495057;
            text-decoration: none;
            transition: all 0.2s;
            cursor: pointer;
        }

        .sidebar-item:hover, .sidebar-header-item:hover {
            background: #e9ecef;
            color: #005E60;
            text-decoration: none;
        }

        .sidebar-item.active {
            background: #005E60;
            color: white;
        }

        .sidebar-item i, .sidebar-header-item i {
            width: 20px;
            margin-right: 0.75rem;
        }

        .sidebar-subheader {
            display: flex;
            align-items: center;
            padding: 0.5rem 1rem 0.5rem 2rem;
            color: #6c757d;
            font-size: 0.9rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }

        .sidebar-subheader:hover {
            background: #f1f3f4;
            color: #005E60;
        }

        .sidebar-subitem {
            display: flex;
            align-items: center;
            padding: 0.5rem 1rem 0.5rem 3rem;
            color: #6c757d;
            text-decoration: none;
            font-size: 0.9rem;
            transition: all 0.2s;
        }

        .sidebar-subitem:hover {
            background: #f1f3f4;
            color: #005E60;
            text-decoration: none;
        }

        .sidebar-subitem.active {
            background: #005E60;
            color: white;
        }

        .sidebar-subitem i {
            width: 16px;
            margin-right: 0.5rem;
        }

        .main-content {
            margin-left: 0;
            transition: margin-left 0.3s ease;
            min-height: calc(100vh - 60px);
        }

        .main-content.sidebar-open {
            margin-left: 280px;
        }

        .sidebar-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            width: 100%;
            height: calc(100vh - 60px);
            background: rgba(0, 0, 0, 0.5);
            z-index: 999;
            display: none;
        }

        .sidebar-overlay.show {
            display: block;
        }

        @media (max-width: 768px) {
            .main-content.sidebar-open {
                margin-left: 0;
            }
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Top Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-vernova fixed-top">
        <div class="container-fluid">
            <button class="btn btn-outline-light me-3" type="button" id="sidebarToggle">
                <i class="fas fa-bars"></i>
            </button>

            <a class="navbar-brand d-flex align-items-center" href="{% url 'inventory:dashboard' %}">
                <img src="{% static 'images/ge_vernova.svg' %}" alt="GE Vernova" height="35" class="me-2">
                <span class="fw-bold">Cloud Central SS</span>
            </a>

            <div class="ms-auto">
                <ul class="navbar-nav">
                    {% if user.is_authenticated %}
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user-circle me-2"></i>
                                <span>{{ user.get_full_name|default:user.username }}</span>
                                <span class="badge bg-secondary ms-2">
                                    {% if user.groups.first %}{{ user.groups.first.name }}{% else %}User{% endif %}
                                </span>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><a class="dropdown-item" href="{% url 'authentication:profile' %}">
                                    <i class="fas fa-user-circle me-2"></i>Profile
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{% url 'authentication:logout' %}">
                                    <i class="fas fa-sign-out-alt me-2"></i>Logout
                                </a></li>
                            </ul>
                        </li>
                    {% else %}
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'authentication:login' %}">
                                <i class="fas fa-sign-in-alt me-1"></i>Login
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- Sidebar Overlay -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>

    <!-- Sidebar Navigation -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <!-- <i class="fas fa-cloud me-2"></i>Navigation -->
        </div>

        <div class="sidebar-content">
            <!-- Dashboard -->
            <div class="sidebar-section">
                <a href="{% url 'inventory:dashboard' %}" class="sidebar-item {% if request.resolver_match.url_name == 'dashboard' and request.resolver_match.namespace == 'inventory' %}active{% endif %}">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Dashboard</span>
                </a>
            </div>

            <!-- Inventory Section -->
            <div class="sidebar-section">
                <div class="sidebar-header-item" data-bs-toggle="collapse" data-bs-target="#inventoryMenu">
                    <i class="fas fa-server"></i>
                    <span>Inventory</span>
                    <i class="fas fa-chevron-down ms-auto"></i>
                </div>

                <div class="collapse show" id="inventoryMenu">
                    <!-- AWS Subsection -->
                    <div class="sidebar-subsection">
                        <div class="sidebar-subheader" data-bs-toggle="collapse" data-bs-target="#awsMenu">
                            <i class="fab fa-aws"></i>
                            <span>AWS</span>
                            <i class="fas fa-chevron-down ms-auto"></i>
                        </div>

                        <div class="collapse show" id="awsMenu">
                            <a href="{% url 'inventory:resource_type_list' 'ec2' %}" class="sidebar-subitem {% if request.resolver_match.url_name == 'resource_type_list' and 'ec2' in request.path %}active{% endif %}">
                                <i class="fas fa-desktop"></i>
                                <span>EC2 Instances</span>
                            </a>
                            <a href="{% url 'inventory:resource_type_list' 's3' %}" class="sidebar-subitem {% if request.resolver_match.url_name == 'resource_type_list' and 's3' in request.path %}active{% endif %}">
                                <i class="fas fa-hdd"></i>
                                <span>S3 Buckets</span>
                            </a>
                            <a href="{% url 'inventory:resource_type_list' 'lambda' %}" class="sidebar-subitem {% if request.resolver_match.url_name == 'resource_type_list' and 'lambda' in request.path %}active{% endif %}">
                                <i class="fas fa-bolt"></i>
                                <span>Lambda Functions</span>
                            </a>
                            <a href="{% url 'inventory:resource_type_list' 'eks' %}" class="sidebar-subitem {% if request.resolver_match.url_name == 'resource_type_list' and 'eks' in request.path %}active{% endif %}">
                                <i class="fas fa-dharmachakra"></i>
                                <span>EKS Clusters</span>
                            </a>
                            <a href="{% url 'inventory:resource_type_list' 'ecs' %}" class="sidebar-subitem {% if request.resolver_match.url_name == 'resource_type_list' and 'ecs' in request.path %}active{% endif %}">
                                <i class="fas fa-cubes"></i>
                                <span>ECS Clusters</span>
                            </a>
                            <a href="{% url 'inventory:resource_type_list' 'ecr' %}" class="sidebar-subitem {% if request.resolver_match.url_name == 'resource_type_list' and 'ecr' in request.path %}active{% endif %}">
                                <i class="fas fa-box"></i>
                                <span>ECR Repositories</span>
                            </a>
                        </div>
                    </div>

                    <!-- Jobs Subsection -->
                    <div class="sidebar-subsection">
                        <div class="sidebar-subheader" data-bs-toggle="collapse" data-bs-target="#inventoryJobsMenu">
                            <i class="fas fa-tasks"></i>
                            <span>Data Collection Jobs</span>
                            <i class="fas fa-chevron-down ms-auto"></i>
                        </div>

                        <div class="collapse" id="inventoryJobsMenu">
                            <a href="{% url 'jobs:job_list' %}?category=inventory" class="sidebar-subitem {% if request.resolver_match.app_name == 'jobs' and 'inventory' in request.GET.category|default:'' %}active{% endif %}">
                                <i class="fas fa-database"></i>
                                <span>Collection Jobs</span>
                            </a>
                            <a href="{% url 'jobs:execution_list' %}?category=inventory" class="sidebar-subitem">
                                <i class="fas fa-history"></i>
                                <span>Job History</span>
                            </a>
                        </div>
                    </div>

                    <!-- Azure Subsection -->
                    <div class="sidebar-subsection">
                        <div class="sidebar-subheader" data-bs-toggle="collapse" data-bs-target="#azureMenu">
                            <i class="fab fa-microsoft"></i>
                            <span>Azure</span>
                            <i class="fas fa-chevron-down ms-auto"></i>
                        </div>

                        <div class="collapse" id="azureMenu">
                            <a href="/azure/vm/" class="sidebar-subitem">
                                <i class="fas fa-desktop"></i>
                                <span>Virtual Machines</span>
                                <span class="badge bg-warning ms-auto">Soon</span>
                            </a>
                            <a href="/azure/storage/" class="sidebar-subitem">
                                <i class="fas fa-hdd"></i>
                                <span>Storage Accounts</span>
                                <span class="badge bg-warning ms-auto">Soon</span>
                            </a>
                            <a href="/azure/aks/" class="sidebar-subitem">
                                <i class="fas fa-dharmachakra"></i>
                                <span>AKS Clusters</span>
                                <span class="badge bg-warning ms-auto">Soon</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Operations Section -->
            <div class="sidebar-section">
                <div class="sidebar-header-item" data-bs-toggle="collapse" data-bs-target="#operationsMenu">
                    <i class="fas fa-cogs"></i>
                    <span>Operations</span>
                    <i class="fas fa-chevron-down ms-auto"></i>
                </div>

                <div class="collapse show" id="operationsMenu">
                    <!-- Automation Subsection -->
                    <div class="sidebar-subsection">
                        <div class="sidebar-subheader" data-bs-toggle="collapse" data-bs-target="#automationMenu">
                            <i class="fas fa-robot"></i>
                            <span>Automation</span>
                            <i class="fas fa-chevron-down ms-auto"></i>
                        </div>

                        <div class="collapse show" id="automationMenu">
                            <a href="{% url 'operations:template_list' %}" class="sidebar-subitem {% if request.resolver_match.url_name == 'template_list' and request.resolver_match.namespace == 'operations' %}active{% endif %}">
                                <i class="fas fa-file-code"></i>
                                <span>Templates</span>
                                <span class="badge bg-info ms-auto">New</span>
                            </a>
                            <a href="{% url 'operations:adhoc_list' %}" class="sidebar-subitem {% if request.resolver_match.url_name == 'adhoc_list' and request.resolver_match.namespace == 'operations' %}active{% endif %}">
                                <i class="fas fa-tasks"></i>
                                <span>Automation Jobs</span>
                            </a>
                            <a href="{% url 'operations:adhoc_create' %}" class="sidebar-subitem {% if request.resolver_match.url_name == 'adhoc_create' and request.resolver_match.namespace == 'operations' %}active{% endif %}">
                                <i class="fas fa-play-circle"></i>
                                <span>Ad-Hoc Runs</span>
                                <span class="badge bg-success ms-auto">Beta</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content" id="mainContent">
        <div class="container-fluid py-4">
            <!-- Messages -->
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}

            <!-- Breadcrumb -->
            {% block breadcrumb %}{% endblock %}

            <!-- Page content -->
            {% block content %}{% endblock %}
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-light py-3 mt-5">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-6">
                    <p class="mb-0 text-muted">
                        <small>&copy; 2024 GE Vernova. Cloud Central SS - Multi-Cloud Resource Inventory</small>
                    </p>
                </div>
                <div class="col-md-6 text-end">
                    <p class="mb-0 text-muted">
                        <small>Version 2.0.0 - Enhanced Edition</small>
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Custom JS -->
    <script src="{% static 'js/custom.js' %}"></script>

    <script>
        // Sidebar toggle functionality
        document.addEventListener('DOMContentLoaded', function() {
            const sidebarToggle = document.getElementById('sidebarToggle');
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('mainContent');
            const sidebarOverlay = document.getElementById('sidebarOverlay');

            function toggleSidebar() {
                sidebar.classList.toggle('show');
                mainContent.classList.toggle('sidebar-open');

                if (window.innerWidth <= 768) {
                    sidebarOverlay.classList.toggle('show');
                }
            }

            sidebarToggle.addEventListener('click', toggleSidebar);
            sidebarOverlay.addEventListener('click', toggleSidebar);

            // Auto-show sidebar on larger screens
            if (window.innerWidth > 768) {
                sidebar.classList.add('show');
                mainContent.classList.add('sidebar-open');
            }

            // Handle window resize
            window.addEventListener('resize', function() {
                if (window.innerWidth > 768) {
                    sidebarOverlay.classList.remove('show');
                    sidebar.classList.add('show');
                    mainContent.classList.add('sidebar-open');
                } else {
                    if (sidebar.classList.contains('show')) {
                        sidebarOverlay.classList.add('show');
                    }
                    mainContent.classList.remove('sidebar-open');
                }
            });
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
