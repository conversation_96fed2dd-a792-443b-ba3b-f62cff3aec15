/* GE Vernova Brand Colors */
:root {
    --vernova-primary: #005E60;
    --vernova-primary-light: #007B7E;
    --vernova-primary-dark: #004548;
    --vernova-secondary: #00A3A6;
    --vernova-accent: #00C5C9;
    --vernova-gray: #6C757D;
    --vernova-light-gray: #F8F9FA;
    --vernova-success: #28A745;
    --vernova-warning: #FFC107;
    --vernova-danger: #DC3545;
    --vernova-info: #17A2B8;
}

/* Custom Bootstrap theme with GE Vernova colors */
.bg-vernova {
    background-color: var(--vernova-primary) !important;
}

.text-vernova {
    color: var(--vernova-primary) !important;
}

.btn-vernova {
    background-color: var(--vernova-primary);
    border-color: var(--vernova-primary);
    color: white;
}

.btn-vernova:hover {
    background-color: var(--vernova-primary-dark);
    border-color: var(--vernova-primary-dark);
    color: white;
}

.btn-outline-vernova {
    color: var(--vernova-primary);
    border-color: var(--vernova-primary);
}

.btn-outline-vernova:hover {
    background-color: var(--vernova-primary);
    border-color: var(--vernova-primary);
    color: white;
}

/* Navigation customizations */
.navbar-brand img {
    filter: brightness(0) invert(1);
}

.navbar-dark .navbar-nav .nav-link.active {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 0.375rem;
}

/* Card customizations */
.card-header-vernova {
    background-color: var(--vernova-primary);
    color: white;
    border-bottom: 1px solid var(--vernova-primary-dark);
}

.card-vernova {
    border-color: var(--vernova-primary);
}

/* Status badges */
.badge-running {
    background-color: var(--vernova-info);
}

.badge-completed {
    background-color: var(--vernova-success);
}

.badge-failed {
    background-color: var(--vernova-danger);
}

.badge-pending {
    background-color: var(--vernova-warning);
}

.badge-active {
    background-color: var(--vernova-success);
}

.badge-inactive {
    background-color: var(--vernova-gray);
}

/* Progress bars */
.progress-vernova .progress-bar {
    background-color: var(--vernova-primary);
}

/* Tables */
.table-vernova {
    --bs-table-accent-bg: var(--vernova-light-gray);
}

.table-vernova thead th {
    background-color: var(--vernova-primary);
    color: white;
    border-color: var(--vernova-primary-dark);
}

/* Forms */
.form-control:focus {
    border-color: var(--vernova-primary);
    box-shadow: 0 0 0 0.2rem rgba(0, 94, 96, 0.25);
}

.form-select:focus {
    border-color: var(--vernova-primary);
    box-shadow: 0 0 0 0.2rem rgba(0, 94, 96, 0.25);
}

/* Dashboard cards */
.dashboard-card {
    transition: transform 0.2s ease-in-out;
    border-left: 4px solid var(--vernova-primary);
}

.dashboard-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.dashboard-stat {
    font-size: 2rem;
    font-weight: bold;
    color: var(--vernova-primary);
}

/* Resource type icons */
.resource-icon {
    width: 24px;
    height: 24px;
    margin-right: 8px;
}

.resource-ec2 {
    color: #FF9900;
}

.resource-s3 {
    color: #569A31;
}

.resource-eks {
    color: #FF9900;
}

.resource-ecs {
    color: #FF9900;
}

.resource-ecr {
    color: #FF9900;
}

.resource-lambda {
    color: #FF9900;
}

/* Job status indicators */
.job-status {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.job-status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
}

.status-running .job-status-indicator {
    background-color: var(--vernova-info);
    animation: pulse 2s infinite;
}

.status-completed .job-status-indicator {
    background-color: var(--vernova-success);
}

.status-failed .job-status-indicator {
    background-color: var(--vernova-danger);
}

.status-pending .job-status-indicator {
    background-color: var(--vernova-warning);
}

/* Additional status badges for EC2 states */
.badge-stopped {
    background-color: var(--vernova-danger);
}

.badge-stopping {
    background-color: #fd7e14;
}

.badge-starting {
    background-color: var(--vernova-info);
}

.badge-terminated {
    background-color: var(--vernova-gray);
}

/* Business Unit badge */
.badge-bu {
    background: linear-gradient(45deg, var(--vernova-primary), var(--vernova-accent));
    color: white;
    font-weight: 600;
}

/* Instance type badge */
.badge-instance-type {
    background-color: var(--vernova-gray);
    color: white;
    font-family: 'Courier New', monospace;
    font-size: 0.7rem;
}

/* Filter section styles */
.filter-section {
    background: var(--vernova-light-gray);
    border: 1px solid #dee2e6;
    border-radius: 8px;
    margin-bottom: 1.5rem;
}

.filter-section .card-header {
    background: #e9ecef;
    color: #495057;
    border-bottom: 1px solid #dee2e6;
}

/* Resource table specific styles */
.resource-table {
    font-size: 0.9rem;
}

.resource-table th {
    background-color: var(--vernova-primary);
    color: white;
    border: none;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.8rem;
    letter-spacing: 0.5px;
    padding: 0.75rem 0.5rem;
    white-space: nowrap;
}

.resource-table td {
    padding: 0.75rem 0.5rem;
    vertical-align: middle;
    border-top: 1px solid #dee2e6;
}

.resource-table code {
    background: var(--vernova-light-gray);
    color: #495057;
    padding: 0.2rem 0.4rem;
    border-radius: 3px;
    font-size: 0.85em;
}

/* Filter form improvements */
.filter-form .form-label {
    font-weight: 600;
    color: #495057;
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.filter-form .form-select,
.filter-form .form-control {
    font-size: 0.9rem;
    border: 1px solid #ced4da;
    border-radius: 4px;
}

.filter-form .form-select:focus,
.filter-form .form-control:focus {
    border-color: var(--vernova-primary);
    box-shadow: 0 0 0 0.2rem rgba(0, 94, 96, 0.25);
}

/* Responsive improvements for tables */
@media (max-width: 768px) {
    .resource-table {
        font-size: 0.8rem;
    }

    .resource-table th,
    .resource-table td {
        padding: 0.5rem 0.25rem;
    }
}

@keyframes pulse {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
    100% {
        opacity: 1;
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .dashboard-stat {
        font-size: 1.5rem;
    }

    .navbar-brand span {
        display: none;
    }
}

/* Loading spinner */
.spinner-vernova {
    border: 3px solid var(--vernova-light-gray);
    border-top: 3px solid var(--vernova-primary);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Sidebar for filters */
.filter-sidebar {
    background-color: var(--vernova-light-gray);
    border-right: 1px solid #dee2e6;
    min-height: calc(100vh - 200px);
}

/* Search box */
.search-box {
    position: relative;
}

.search-box .form-control {
    padding-right: 40px;
}

.search-box .search-icon {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--vernova-gray);
}

/* Alert customizations */
.alert-vernova {
    color: white;
    background-color: var(--vernova-primary);
    border-color: var(--vernova-primary-dark);
}

/* Footer */
footer {
    border-top: 1px solid #dee2e6;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--vernova-light-gray);
}

::-webkit-scrollbar-thumb {
    background: var(--vernova-gray);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--vernova-primary);
}
