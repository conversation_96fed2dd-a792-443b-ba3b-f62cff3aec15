from django.db import models
from django.contrib.auth.models import User
import uuid


class AzureSession(models.Model):
    """Azure session management for multi-cloud support"""
    AUTH_METHODS = [
        ('service_principal', 'Service Principal'),
        ('managed_identity', 'Managed Identity'),
        ('azure_cli', 'Azure CLI'),
        ('device_code', 'Device Code'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    auth_method = models.CharField(max_length=20, choices=AUTH_METHODS)

    # Service Principal credentials
    tenant_id = models.CharField(max_length=100, blank=True)
    client_id = models.CharField(max_length=100, blank=True)
    client_secret = models.Char<PERSON>ield(max_length=200, blank=True)

    # Subscription details
    subscription_id = models.CharField(max_length=100, blank=True)
    subscription_name = models.CharField(max_length=100, blank=True)

    # Configuration
    default_resource_group = models.CharField(max_length=100, blank=True)
    default_location = models.CharField(max_length=50, blank=True)

    # Metadata
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='azure_sessions')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)
    last_tested = models.DateTimeField(null=True, blank=True)
    test_status = models.CharField(max_length=20, default='untested')

    class Meta:
        verbose_name = "Azure Session"
        verbose_name_plural = "Azure Sessions"

    def __str__(self):
        return f"{self.name} ({self.auth_method})"


class AppConfiguration(models.Model):
    """Application-wide configuration settings"""
    CONFIG_TYPES = [
        ('string', 'String'),
        ('integer', 'Integer'),
        ('boolean', 'Boolean'),
        ('json', 'JSON'),
        ('text', 'Text'),
    ]

    key = models.CharField(max_length=100, unique=True)
    value = models.TextField()
    config_type = models.CharField(max_length=10, choices=CONFIG_TYPES, default='string')
    description = models.TextField(blank=True)
    category = models.CharField(max_length=50, default='general')
    is_sensitive = models.BooleanField(default=False)

    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "App Configuration"
        verbose_name_plural = "App Configurations"
        ordering = ['category', 'key']

    def __str__(self):
        return f"{self.key} ({self.category})"

    def get_typed_value(self):
        """Return the value in its proper type"""
        if self.config_type == 'integer':
            return int(self.value)
        elif self.config_type == 'boolean':
            return self.value.lower() in ('true', '1', 'yes', 'on')
        elif self.config_type == 'json':
            import json
            return json.loads(self.value)
        else:
            return self.value


class UserManagement(models.Model):
    """User management and audit trail"""
    ACTION_TYPES = [
        ('create', 'User Created'),
        ('update', 'User Updated'),
        ('delete', 'User Deleted'),
        ('activate', 'User Activated'),
        ('deactivate', 'User Deactivated'),
        ('group_change', 'Group Changed'),
        ('permission_change', 'Permission Changed'),
    ]

    target_user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='management_logs')
    action_type = models.CharField(max_length=20, choices=ACTION_TYPES)
    action_details = models.TextField()
    performed_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='performed_actions')
    timestamp = models.DateTimeField(auto_now_add=True)
    ip_address = models.GenericIPAddressField(null=True, blank=True)

    class Meta:
        verbose_name = "User Management Log"
        verbose_name_plural = "User Management Logs"
        ordering = ['-timestamp']

    def __str__(self):
        return f"{self.action_type} - {self.target_user.username} by {self.performed_by.username}"
