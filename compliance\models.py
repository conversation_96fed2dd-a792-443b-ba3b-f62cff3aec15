from django.db import models
from django.contrib.auth.models import User
from inventory.models import AWSAccount, EKSCluster
import uuid


class PatchCalendar(models.Model):
    """Patch calendar for compliance tracking"""
    PATCH_TYPES = [
        ('security', 'Security Patch'),
        ('critical', 'Critical Update'),
        ('feature', 'Feature Update'),
        ('maintenance', 'Maintenance'),
    ]

    STATUS_CHOICES = [
        ('scheduled', 'Scheduled'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('postponed', 'Postponed'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    title = models.CharField(max_length=200)
    description = models.TextField()
    patch_type = models.CharField(max_length=20, choices=PATCH_TYPES)

    # Scheduling
    scheduled_date = models.DateTimeField()
    maintenance_window_start = models.DateTimeField()
    maintenance_window_end = models.DateTimeField()

    # Target resources
    aws_accounts = models.ManyToManyField(AWSAccount, blank=True)
    resource_types = models.JSONField(default=list)  # ['EC2', 'EKS', etc.]

    # Status tracking
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='scheduled')
    progress_percentage = models.IntegerField(default=0)

    # Metadata
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Patch Calendar Entry"
        verbose_name_plural = "Patch Calendar Entries"
        ordering = ['scheduled_date']

    def __str__(self):
        return f"{self.title} - {self.scheduled_date.strftime('%Y-%m-%d')}"


class EKSNodeRefresh(models.Model):
    """EKS Node refresh tracking and scheduling"""
    REFRESH_TYPES = [
        ('ami_update', 'AMI Update'),
        ('kubernetes_version', 'Kubernetes Version Upgrade'),
        ('security_patch', 'Security Patch'),
        ('scheduled_maintenance', 'Scheduled Maintenance'),
    ]

    STATUS_CHOICES = [
        ('planned', 'Planned'),
        ('scheduled', 'Scheduled'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('rolled_back', 'Rolled Back'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    cluster_name = models.CharField(max_length=100)
    aws_account = models.ForeignKey(AWSAccount, on_delete=models.CASCADE)
    region = models.CharField(max_length=20)

    # Refresh details
    refresh_type = models.CharField(max_length=30, choices=REFRESH_TYPES)
    current_ami = models.CharField(max_length=100, blank=True)
    target_ami = models.CharField(max_length=100, blank=True)
    current_k8s_version = models.CharField(max_length=20, blank=True)
    target_k8s_version = models.CharField(max_length=20, blank=True)

    # Scheduling
    scheduled_date = models.DateTimeField()
    estimated_duration = models.IntegerField(help_text="Duration in minutes")

    # Status and progress
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='planned')
    progress_percentage = models.IntegerField(default=0)
    started_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)

    # Results
    success_count = models.IntegerField(default=0)
    failure_count = models.IntegerField(default=0)
    rollback_required = models.BooleanField(default=False)

    # Metadata
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "EKS Node Refresh"
        verbose_name_plural = "EKS Node Refreshes"
        ordering = ['-scheduled_date']

    def __str__(self):
        return f"{self.cluster_name} - {self.refresh_type} ({self.scheduled_date.strftime('%Y-%m-%d')})"


class AKSNodeRefresh(models.Model):
    """AKS Node refresh tracking and scheduling"""
    REFRESH_TYPES = [
        ('node_image_upgrade', 'Node Image Upgrade'),
        ('kubernetes_version', 'Kubernetes Version Upgrade'),
        ('security_patch', 'Security Patch'),
        ('scheduled_maintenance', 'Scheduled Maintenance'),
    ]

    STATUS_CHOICES = [
        ('planned', 'Planned'),
        ('scheduled', 'Scheduled'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('rolled_back', 'Rolled Back'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    cluster_name = models.CharField(max_length=100)
    subscription_id = models.CharField(max_length=100)
    resource_group = models.CharField(max_length=100)

    # Refresh details
    refresh_type = models.CharField(max_length=30, choices=REFRESH_TYPES)
    current_node_image = models.CharField(max_length=100, blank=True)
    target_node_image = models.CharField(max_length=100, blank=True)
    current_k8s_version = models.CharField(max_length=20, blank=True)
    target_k8s_version = models.CharField(max_length=20, blank=True)

    # Scheduling
    scheduled_date = models.DateTimeField()
    estimated_duration = models.IntegerField(help_text="Duration in minutes")

    # Status and progress
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='planned')
    progress_percentage = models.IntegerField(default=0)
    started_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)

    # Results
    success_count = models.IntegerField(default=0)
    failure_count = models.IntegerField(default=0)
    rollback_required = models.BooleanField(default=False)

    # Metadata
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "AKS Node Refresh"
        verbose_name_plural = "AKS Node Refreshes"
        ordering = ['-scheduled_date']

    def __str__(self):
        return f"{self.cluster_name} - {self.refresh_type} ({self.scheduled_date.strftime('%Y-%m-%d')})"
