from django.urls import path
from . import views

app_name = 'azure'

urlpatterns = [
    # Dashboard
    path('', views.AzureDashboardView.as_view(), name='dashboard'),
    
    # Resource views
    path('resources/', views.AzureResourceListView.as_view(), name='resource_list'),
    path('resources/<str:resource_type>/', views.AzureResourceTypeListView.as_view(), name='resource_type_list'),
    path('resource/<int:pk>/', views.AzureResourceDetailView.as_view(), name='resource_detail'),
    
    # Specific resource types
    path('vm/', views.VirtualMachineListView.as_view(), name='vm_list'),
    path('storage/', views.StorageAccountListView.as_view(), name='storage_list'),
    
    # API endpoints
    path('api/resources/', views.AzureResourceListAPIView.as_view(), name='api_resource_list'),
]
